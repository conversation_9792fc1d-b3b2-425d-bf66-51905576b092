<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h, watch, reactive } from 'vue';
import type { DataTableColumns, DataTableRowKey, FormInst, FormRules } from 'naive-ui';
import { NButton, NTag, NAvatar, NPopconfirm, NDropdown, useMessage, useDialog } from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';

defineOptions({
  name: 'UserManagement'
});

const appStore = useAppStore();
const message = useMessage();
const dialog = useDialog();

interface UserInfo {
  id: string;
  username: string;
  email: string;
  phone: string;
  avatar: string;
  status: 'active' | 'disabled' | 'pending';
  level: 'normal' | 'vip' | 'svip';
  group: string;
  tags: string[];
  registerTime: string;
  lastLoginTime: string;
  orderCount: number;
  totalSpent: number;
}

// 模拟用户数据
const users = ref<UserInfo[]>([
  {
    id: '1',
    username: '张三',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: '',
    status: 'active',
    level: 'vip',
    group: '普通用户',
    tags: ['新用户', '活跃'],
    registerTime: '2024-01-15 10:30:00',
    lastLoginTime: '2025-01-27 09:15:00',
    orderCount: 15,
    totalSpent: 2580.50
  },
  {
    id: '2',
    username: '李四',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: '',
    status: 'active',
    level: 'normal',
    group: '普通用户',
    tags: ['老用户'],
    registerTime: '2023-08-20 14:20:00',
    lastLoginTime: '2025-01-26 16:45:00',
    orderCount: 8,
    totalSpent: 1250.00
  },
  {
    id: '3',
    username: '王五',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: '',
    status: 'disabled',
    level: 'normal',
    group: '普通用户',
    tags: ['问题用户'],
    registerTime: '2024-03-10 09:15:00',
    lastLoginTime: '2025-01-20 11:30:00',
    orderCount: 3,
    totalSpent: 450.00
  },
  {
    id: '4',
    username: '赵六',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: '',
    status: 'pending',
    level: 'normal',
    group: '新用户',
    tags: ['待验证'],
    registerTime: '2025-01-25 16:20:00',
    lastLoginTime: '',
    orderCount: 0,
    totalSpent: 0
  },
  {
    id: '5',
    username: '钱七',
    email: '<EMAIL>',
    phone: '13800138005',
    avatar: '',
    status: 'active',
    level: 'svip',
    group: 'VIP用户',
    tags: ['超级VIP', '大客户'],
    registerTime: '2023-05-12 11:45:00',
    lastLoginTime: '2025-01-27 08:20:00',
    orderCount: 45,
    totalSpent: 15680.00
  }
]);

// 搜索和筛选
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);
const selectedLevel = ref<string | null>(null);
const selectedGroup = ref<string | null>(null);

// 高级筛选状态
const showAdvancedFilter = ref(false);
const selectedTags = ref<string[]>([]);
const selectedIdentity = ref<string | null>(null);
const selectedMemberStatus = ref<string | null>(null);
const selectedBalanceRange = ref<string | null>(null);
const selectedPointsRange = ref<string | null>(null);
const selectedConsumptionTime = ref<string | null>(null);
const selectedOrderRange = ref<string | null>(null);
const selectedSpentRange = ref<string | null>(null);
const selectedRechargeRange = ref<string | null>(null);
const selectedLoginTime = ref<string | null>(null);

// 分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 选中的行
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 表单相关状态
const showUserModal = ref(false);
const isEditMode = ref(false);
const formRef = ref<FormInst | null>(null);

// 用户详情抽屉状态
const showUserDetail = ref(false);
const currentUserId = ref('');
const userNotes = ref('');
const activeTab = ref('userInfo');

// 响应式抽屉宽度
const drawerWidth = computed(() => {
  if (typeof window === 'undefined') return 1000;
  const screenWidth = window.innerWidth;
  if (screenWidth >= 1400) return 1200;
  if (screenWidth >= 1200) return 1000;
  if (screenWidth >= 768) return '80%';
  return '95%';
});

// 当前用户详情数据
const currentUserDetail = computed(() => {
  const user = users.value.find(u => u.id === currentUserId.value);
  if (!user) return null;

  return {
    ...user,
    // 扩展用户详情数据
    balance: 1580.50,
    points: 2350,
    monthlyOrders: 8,
    monthlySpent: 1280.00,
    realName: '张三',
    birthday: '1990-05-15',
    idCard: '110101199005150001',
    address: '北京市朝阳区xxx街道xxx号',
    loginPassword: '******',
    promotionQualification: '有',
    promoter: '李四',
    notes: '优质客户，购买力强',
    // 消费记录
    consumptionRecords: [
      { orderId: 'ORD001', receiver: '张三', quantity: 3, actualAmount: 299.00, completionTime: '2024-01-15 14:30:00' },
      { orderId: 'ORD002', receiver: '张三', quantity: 1, actualAmount: 599.00, completionTime: '2024-01-10 09:15:00' },
      { orderId: 'ORD003', receiver: '张三', quantity: 2, actualAmount: 199.00, completionTime: '2024-01-05 16:45:00' }
    ],
    // 积分明细
    pointsDetail: [
      { source: '购物消费', change: '+50', afterChange: 2350, date: '2024-01-15', remark: '订单ORD001消费获得' },
      { source: '签到奖励', change: '+10', afterChange: 2300, date: '2024-01-14', remark: '每日签到' },
      { source: '推荐好友', change: '+100', afterChange: 2290, date: '2024-01-13', remark: '成功推荐用户注册' }
    ],
    // 签到记录
    signInRecords: [
      { signInDate: '2024-01-15', consecutiveDays: 15, pointsEarned: 10 },
      { signInDate: '2024-01-14', consecutiveDays: 14, pointsEarned: 10 },
      { signInDate: '2024-01-13', consecutiveDays: 13, pointsEarned: 10 }
    ],
    // 持有优惠券
    coupons: [
      { name: '新用户专享券', type: '满减券', amount: '50元', condition: '满200元可用', validity: '2024-02-15', status: '未使用' },
      { name: '生日特惠券', type: '折扣券', amount: '9折', condition: '全场通用', validity: '2024-03-15', status: '未使用' }
    ],
    // 余额变动
    balanceChanges: [
      { changeType: '充值', changeAmount: '+500.00', afterChange: 1580.50, time: '2024-01-15 10:30:00', remark: '在线充值' },
      { changeType: '消费', changeAmount: '-299.00', afterChange: 1080.50, time: '2024-01-15 14:30:00', remark: '订单支付' },
      { changeType: '退款', changeAmount: '+199.00', afterChange: 1379.50, time: '2024-01-12 16:20:00', remark: '订单退款' }
    ],
    // 好友关系
    friendRelations: [
      { friendUsername: '李四', relationType: '推荐关系', establishTime: '2024-01-01 12:00:00' },
      { friendUsername: '王五', relationType: '互相关注', establishTime: '2024-01-05 15:30:00' }
    ]
  };
});

// 消费记录表格列定义
const consumptionColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.consumptionRecords.orderId'),
    key: 'orderId',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.consumptionRecords.receiver'),
    key: 'receiver',
    width: 100
  },
  {
    title: $t('page.userManagement.users.userDetail.consumptionRecords.quantity'),
    key: 'quantity',
    width: 80
  },
  {
    title: $t('page.userManagement.users.userDetail.consumptionRecords.actualAmount'),
    key: 'actualAmount',
    width: 120,
    render: (row: any) => `¥${row.actualAmount.toFixed(2)}`
  },
  {
    title: $t('page.userManagement.users.userDetail.consumptionRecords.completionTime'),
    key: 'completionTime',
    width: 160
  }
]);

// 积分明细表格列定义
const pointsColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.pointsDetail.source'),
    key: 'source',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.pointsDetail.change'),
    key: 'change',
    width: 100,
    render: (row: any) => h('span', {
      class: row.change.startsWith('+') ? 'text-green-500' : 'text-red-500'
    }, row.change)
  },
  {
    title: $t('page.userManagement.users.userDetail.pointsDetail.afterChange'),
    key: 'afterChange',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.pointsDetail.date'),
    key: 'date',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.pointsDetail.remark'),
    key: 'remark',
    ellipsis: {
      tooltip: true
    }
  }
]);

// 签到记录表格列定义
const signInColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.signInRecords.signInDate'),
    key: 'signInDate',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.signInRecords.consecutiveDays'),
    key: 'consecutiveDays',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.signInRecords.pointsEarned'),
    key: 'pointsEarned',
    width: 100
  }
]);

// 优惠券表格列定义
const couponsColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.coupons.name'),
    key: 'name',
    width: 150
  },
  {
    title: $t('page.userManagement.users.userDetail.coupons.type'),
    key: 'type',
    width: 100
  },
  {
    title: $t('page.userManagement.users.userDetail.coupons.amount'),
    key: 'amount',
    width: 100
  },
  {
    title: $t('page.userManagement.users.userDetail.coupons.condition'),
    key: 'condition',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.coupons.validity'),
    key: 'validity',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.coupons.status'),
    key: 'status',
    width: 100,
    render: (row: any) => h(NTag, {
      type: row.status === '未使用' ? 'success' : 'default',
      size: 'small'
    }, { default: () => row.status })
  }
]);

// 余额变动表格列定义
const balanceColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.balanceChanges.changeType'),
    key: 'changeType',
    width: 100
  },
  {
    title: $t('page.userManagement.users.userDetail.balanceChanges.changeAmount'),
    key: 'changeAmount',
    width: 120,
    render: (row: any) => h('span', {
      class: row.changeAmount.startsWith('+') ? 'text-green-500' : 'text-red-500'
    }, row.changeAmount)
  },
  {
    title: $t('page.userManagement.users.userDetail.balanceChanges.afterChange'),
    key: 'afterChange',
    width: 120,
    render: (row: any) => `¥${row.afterChange}`
  },
  {
    title: $t('page.userManagement.users.userDetail.balanceChanges.time'),
    key: 'time',
    width: 160
  },
  {
    title: $t('page.userManagement.users.userDetail.balanceChanges.remark'),
    key: 'remark',
    ellipsis: {
      tooltip: true
    }
  }
]);

// 好友关系表格列定义
const friendsColumns = computed(() => [
  {
    title: $t('page.userManagement.users.userDetail.friendRelations.friendUsername'),
    key: 'friendUsername',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.friendRelations.relationType'),
    key: 'relationType',
    width: 120
  },
  {
    title: $t('page.userManagement.users.userDetail.friendRelations.establishTime'),
    key: 'establishTime',
    width: 160
  }
]);

// 表单数据
const formData = reactive({
  id: '',
  username: '',
  email: '',
  phone: '',
  password: '',
  confirmPassword: '',
  status: 'active' as 'active' | 'disabled' | 'pending',
  level: 'normal' as 'normal' | 'vip' | 'svip',
  group: '普通用户',
  tags: [] as string[],
  avatar: ''
});

// 表单验证规则
const formRules: FormRules = {
  username: [
    { required: true, message: () => $t('page.userManagement.users.form.usernameRequired'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: () => $t('page.userManagement.users.form.emailRequired'), trigger: 'blur' },
    { type: 'email', message: () => $t('page.userManagement.users.form.emailInvalid'), trigger: 'blur' }
  ],
  phone: [
    { required: true, message: () => $t('page.userManagement.users.form.phoneRequired'), trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: () => $t('page.userManagement.users.form.phoneInvalid'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: () => $t('page.userManagement.users.form.passwordRequired'), trigger: 'blur' },
    { min: 6, message: () => $t('page.userManagement.users.form.passwordMinLength'), trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: () => $t('page.userManagement.users.form.confirmPasswordRequired'), trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        return value === formData.password;
      },
      message: () => $t('page.userManagement.users.form.passwordNotMatch'),
      trigger: 'blur'
    }
  ]
};

// 筛选选项
const statusOptions = computed(() => [
  { label: $t('page.userManagement.users.status.active'), value: 'active' },
  { label: $t('page.userManagement.users.status.disabled'), value: 'disabled' },
  { label: $t('page.userManagement.users.status.pending'), value: 'pending' }
]);

const levelOptions = computed(() => [
  { label: $t('page.userManagement.users.level.normal'), value: 'normal' },
  { label: $t('page.userManagement.users.level.vip'), value: 'vip' },
  { label: $t('page.userManagement.users.level.svip'), value: 'svip' }
]);

const groupOptions = computed(() => [
  { label: $t('page.userManagement.users.group.normal'), value: '普通用户' },
  { label: $t('page.userManagement.users.group.new'), value: '新用户' },
  { label: $t('page.userManagement.users.group.vip'), value: 'VIP用户' }
]);

// 高级筛选选项数据
const tagsOptions = [
  { label: '优质客户', value: 'quality' },
  { label: '活跃用户', value: 'active' },
  { label: '新用户', value: 'new' },
  { label: '老客户', value: 'old' },
  { label: '高消费', value: 'high-spender' }
];

const identityOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.identity.normal'), value: 'normal' },
  { label: $t('page.userManagement.users.filterOptions.identity.merchant'), value: 'merchant' },
  { label: $t('page.userManagement.users.filterOptions.identity.agent'), value: 'agent' },
  { label: $t('page.userManagement.users.filterOptions.identity.admin'), value: 'admin' }
]);

const memberStatusOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.memberStatus.yes'), value: 'yes' },
  { label: $t('page.userManagement.users.filterOptions.memberStatus.no'), value: 'no' }
]);

const balanceRangeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.balanceRange.range1'), value: 'range1' },
  { label: $t('page.userManagement.users.filterOptions.balanceRange.range2'), value: 'range2' },
  { label: $t('page.userManagement.users.filterOptions.balanceRange.range3'), value: 'range3' },
  { label: $t('page.userManagement.users.filterOptions.balanceRange.range4'), value: 'range4' },
  { label: $t('page.userManagement.users.filterOptions.balanceRange.range5'), value: 'range5' }
]);

const pointsRangeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.pointsRange.range1'), value: 'range1' },
  { label: $t('page.userManagement.users.filterOptions.pointsRange.range2'), value: 'range2' },
  { label: $t('page.userManagement.users.filterOptions.pointsRange.range3'), value: 'range3' },
  { label: $t('page.userManagement.users.filterOptions.pointsRange.range4'), value: 'range4' }
]);

const consumptionTimeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.consumptionTime.days7'), value: 'days7' },
  { label: $t('page.userManagement.users.filterOptions.consumptionTime.days30'), value: 'days30' },
  { label: $t('page.userManagement.users.filterOptions.consumptionTime.days90'), value: 'days90' },
  { label: $t('page.userManagement.users.filterOptions.consumptionTime.days180'), value: 'days180' },
  { label: $t('page.userManagement.users.filterOptions.consumptionTime.never'), value: 'never' }
]);

const orderRangeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.orderRange.range1'), value: 'range1' },
  { label: $t('page.userManagement.users.filterOptions.orderRange.range2'), value: 'range2' },
  { label: $t('page.userManagement.users.filterOptions.orderRange.range3'), value: 'range3' },
  { label: $t('page.userManagement.users.filterOptions.orderRange.range4'), value: 'range4' }
]);

const spentRangeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.spentRange.range1'), value: 'range1' },
  { label: $t('page.userManagement.users.filterOptions.spentRange.range2'), value: 'range2' },
  { label: $t('page.userManagement.users.filterOptions.spentRange.range3'), value: 'range3' },
  { label: $t('page.userManagement.users.filterOptions.spentRange.range4'), value: 'range4' },
  { label: $t('page.userManagement.users.filterOptions.spentRange.range5'), value: 'range5' }
]);

const rechargeRangeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.rechargeRange.range1'), value: 'range1' },
  { label: $t('page.userManagement.users.filterOptions.rechargeRange.range2'), value: 'range2' },
  { label: $t('page.userManagement.users.filterOptions.rechargeRange.range3'), value: 'range3' },
  { label: $t('page.userManagement.users.filterOptions.rechargeRange.range4'), value: 'range4' }
]);

const loginTimeOptions = computed(() => [
  { label: $t('page.userManagement.users.filterOptions.loginTime.today'), value: 'today' },
  { label: $t('page.userManagement.users.filterOptions.loginTime.days7'), value: 'days7' },
  { label: $t('page.userManagement.users.filterOptions.loginTime.days30'), value: 'days30' },
  { label: $t('page.userManagement.users.filterOptions.loginTime.days90'), value: 'days90' },
  { label: $t('page.userManagement.users.filterOptions.loginTime.never'), value: 'never' }
]);

// 过滤后的用户数据
const filteredUsers = computed(() => {
  let result = users.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(user => 
      user.username.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      user.phone.includes(query)
    );
  }
  
  if (selectedStatus.value) {
    result = result.filter(user => user.status === selectedStatus.value);
  }
  
  if (selectedLevel.value) {
    result = result.filter(user => user.level === selectedLevel.value);
  }
  
  if (selectedGroup.value) {
    result = result.filter(user => user.group === selectedGroup.value);
  }
  
  return result;
});

// 分页后的数据
const paginatedUsers = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredUsers.value.slice(start, end);
});

// 更新分页信息
function updatePagination() {
  pagination.value.total = filteredUsers.value.length;
}

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: $t('page.userManagement.users.columns.avatar'),
    key: 'avatar',
    width: 80,
    render: (row: any) => h(NAvatar, {
      size: 'medium',
      src: row.avatar || `/src/assets/imgs/avatars/default-${(parseInt(row.id) % 5) + 1}.jpg`,
      fallbackSrc: `https://ui-avatars.com/api/?name=${row.username}&size=40&background=random`
    })
  },
  {
    title: $t('page.userManagement.users.columns.username'),
    key: 'username',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.userManagement.users.columns.email'),
    key: 'email',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.userManagement.users.columns.phone'),
    key: 'phone',
    width: 130
  },
  {
    title: $t('page.userManagement.users.columns.status'),
    key: 'status',
    width: 100,
    render: (row: any) => {
      const statusMap = {
        active: { type: 'success' as const, label: $t('page.userManagement.users.status.active') },
        disabled: { type: 'error' as const, label: $t('page.userManagement.users.status.disabled') },
        pending: { type: 'warning' as const, label: $t('page.userManagement.users.status.pending') }
      };
      const config = statusMap[row.status];
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: $t('page.userManagement.users.columns.level'),
    key: 'level',
    width: 100,
    render: (row: any) => {
      const levelMap = {
        normal: { type: 'default' as const, label: $t('page.userManagement.users.level.normal') },
        vip: { type: 'warning' as const, label: $t('page.userManagement.users.level.vip') },
        svip: { type: 'error' as const, label: $t('page.userManagement.users.level.svip') }
      };
      const config = levelMap[row.level];
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: $t('page.userManagement.users.columns.group'),
    key: 'group',
    width: 100
  },
  {
    title: $t('page.userManagement.users.columns.tags'),
    key: 'tags',
    width: 150,
    render: (row: any) => {
      return h('div', { class: 'flex flex-wrap gap-4px' },
        row.tags.slice(0, 2).map((tag: any) =>
          h(NTag, { size: 'small', type: 'info' }, { default: () => tag })
        ).concat(
          row.tags.length > 2 ? [h(NTag, { size: 'small' }, { default: () => `+${row.tags.length - 2}` })] : []
        )
      );
    }
  },
  {
    title: $t('page.userManagement.users.columns.orderCount'),
    key: 'orderCount',
    width: 100,
    render: (row: any) => row.orderCount.toString()
  },
  {
    title: $t('page.userManagement.users.columns.totalSpent'),
    key: 'totalSpent',
    width: 120,
    render: (row: any) => `¥${row.totalSpent.toFixed(2)}`
  },
  {
    title: $t('page.userManagement.users.columns.registerTime'),
    key: 'registerTime',
    width: 160,
    render: (row: any) => row.registerTime.split(' ')[0]
  },
  {
    title: $t('page.userManagement.users.columns.actions'),
    key: 'actions',
    width: 150,
    fixed: 'right',
    render: (row: any) => {
      return h('div', { class: 'flex items-center space-x-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          onClick: () => handleView(row.id)
        }, { default: () => $t('page.userManagement.users.actions.view') }),
        h(NDropdown, {
          trigger: 'click',
          options: [
            {
              label: $t('page.userManagement.users.actions.edit'),
              key: 'edit',
              icon: () => h('i', { class: 'i-mdi-pencil' })
            },
            {
              label: row.status === 'active'
                ? $t('page.userManagement.users.statusActions.disable')
                : $t('page.userManagement.users.statusActions.enable'),
              key: 'toggle-status',
              icon: () => h('i', { class: row.status === 'active' ? 'i-mdi-account-off' : 'i-mdi-account-check' })
            },
            {
              type: 'divider',
              key: 'divider1'
            },
            {
              label: $t('page.userManagement.users.actions.modifyBalance'),
              key: 'modify-balance',
              icon: () => h('i', { class: 'i-mdi-cash' })
            },
            {
              label: $t('page.userManagement.users.actions.modifyPoints'),
              key: 'modify-points',
              icon: () => h('i', { class: 'i-mdi-star' })
            },
            {
              label: $t('page.userManagement.users.actions.giftMembership'),
              key: 'gift-membership',
              icon: () => h('i', { class: 'i-mdi-crown' })
            },
            {
              label: $t('page.userManagement.users.actions.setGroup'),
              key: 'set-group',
              icon: () => h('i', { class: 'i-mdi-account-group' })
            },
            {
              label: $t('page.userManagement.users.actions.setTags'),
              key: 'set-tags',
              icon: () => h('i', { class: 'i-mdi-tag' })
            },
            {
              label: $t('page.userManagement.users.actions.setPromoter'),
              key: 'set-promoter',
              icon: () => h('i', { class: 'i-mdi-account-arrow-up' })
            },
            {
              type: 'divider',
              key: 'divider2'
            },
            {
              label: $t('page.userManagement.users.actions.delete'),
              key: 'delete',
              icon: () => h('i', { class: 'i-mdi-delete' }),
              props: {
                style: 'color: #d03050;'
              }
            }
          ],
          onSelect: (key: string) => handleDropdownAction(key, row)
        }, {
          default: () => h(NButton, {
            size: 'small',
            text: true,
            type: 'default'
          }, {
            default: () => $t('page.userManagement.users.actions.more'),
            icon: () => h('i', { class: 'i-mdi-dots-horizontal' })
          })
        })
      ]);
    }
  }
]);

// 操作函数
function handleSearch() {
  pagination.value.page = 1;
  updatePagination();
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  selectedLevel.value = null;
  selectedGroup.value = null;
  pagination.value.page = 1;
  updatePagination();
}

// 重置所有筛选条件
function handleResetAllFilters() {
  // 基础筛选
  searchQuery.value = '';
  selectedStatus.value = null;
  selectedLevel.value = null;
  selectedGroup.value = null;

  // 高级筛选
  selectedTags.value = [];
  selectedIdentity.value = null;
  selectedMemberStatus.value = null;
  selectedBalanceRange.value = null;
  selectedPointsRange.value = null;
  selectedConsumptionTime.value = null;
  selectedOrderRange.value = null;
  selectedSpentRange.value = null;
  selectedRechargeRange.value = null;
  selectedLoginTime.value = null;

  pagination.value.page = 1;
  updatePagination();
}

// 切换高级筛选显示
function toggleAdvancedFilter() {
  showAdvancedFilter.value = !showAdvancedFilter.value;
}

function handleView(id: string) {
  currentUserId.value = id;
  const user = users.value.find(u => u.id === id);
  if (user) {
    userNotes.value = '优质客户，购买力强'; // 模拟数据
  }
  activeTab.value = 'userInfo'; // 重置到第一个选项卡
  showUserDetail.value = true;
}

function handleEdit(id: string) {
  handleEditUser(id);
}

function handleDelete(id: string) {
  const index = users.value.findIndex(user => user.id === id);
  if (index > -1) {
    users.value.splice(index, 1);
    message.success($t('page.userManagement.users.deleteSuccess'));
    updatePagination();
  }
}

function handleBatchEnable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.users.pleaseSelectUsers'));
    return;
  }
  message.success($t('page.userManagement.users.batchEnableSuccess'));
}

function handleBatchDisable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.users.pleaseSelectUsers'));
    return;
  }
  message.success($t('page.userManagement.users.batchDisableSuccess'));
}

function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.users.pleaseSelectUsers'));
    return;
  }

  dialog.warning({
    title: $t('page.userManagement.users.batchDelete'),
    content: $t('page.userManagement.users.confirmBatchDelete', { count: checkedRowKeys.value.length }),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      users.value = users.value.filter(user => !checkedRowKeys.value.includes(user.id));
      checkedRowKeys.value = [];
      message.success($t('page.userManagement.users.deleteSuccess'));
      updatePagination();
    }
  });
}

function handleExport() {
  message.info($t('page.userManagement.users.exportingData'));
}

function handleAddUser() {
  isEditMode.value = false;
  resetForm();
  showUserModal.value = true;
}

function resetForm() {
  Object.assign(formData, {
    id: '',
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    status: 'active',
    level: 'normal',
    group: '普通用户',
    tags: [],
    avatar: ''
  });
}

function handleEditUser(id: string) {
  const user = users.value.find(u => u.id === id);
  if (user) {
    isEditMode.value = true;
    Object.assign(formData, {
      ...user,
      password: '',
      confirmPassword: ''
    });
    showUserModal.value = true;
  }
}

function handleSaveUser() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (isEditMode.value) {
        // 编辑用户
        const index = users.value.findIndex(u => u.id === formData.id);
        if (index > -1) {
          users.value[index] = {
            ...users.value[index],
            username: formData.username,
            email: formData.email,
            phone: formData.phone,
            status: formData.status,
            level: formData.level,
            group: formData.group,
            tags: formData.tags,
            avatar: formData.avatar
          };
          message.success($t('common.updateSuccess'));
        }
      } else {
        // 新增用户
        const newUser: UserInfo = {
          id: Date.now().toString(),
          username: formData.username,
          email: formData.email,
          phone: formData.phone,
          avatar: formData.avatar,
          status: formData.status,
          level: formData.level,
          group: formData.group,
          tags: formData.tags,
          registerTime: new Date().toLocaleString(),
          lastLoginTime: '',
          orderCount: 0,
          totalSpent: 0
        };
        users.value.unshift(newUser);
        message.success($t('common.addSuccess'));
      }
      showUserModal.value = false;
      updatePagination();
    }
  });
}

function handleCancelUser() {
  showUserModal.value = false;
  resetForm();
}

// 快捷操作处理函数
function handleModifyBalance() {
  message.info($t('page.userManagement.users.userDetail.quickActions.modifyBalance'));
}

function handleModifyPoints() {
  message.info($t('page.userManagement.users.userDetail.quickActions.modifyPoints'));
}

function handleGiftCoupon() {
  message.info($t('page.userManagement.users.userDetail.quickActions.giftCoupon'));
}

function handleGiftMembership() {
  message.info($t('page.userManagement.users.userDetail.quickActions.giftMembership'));
}

function handleSetTags() {
  message.info($t('page.userManagement.users.userDetail.quickActions.setTags'));
}

function handleSetGroup() {
  message.info($t('page.userManagement.users.userDetail.quickActions.setGroup'));
}

function handleSetPromoter() {
  message.info($t('page.userManagement.users.userDetail.quickActions.setPromoter'));
}

function handleEditNotes() {
  message.info($t('page.userManagement.users.userDetail.quickActions.editNotes'));
}

// 头像上传相关
const uploadAction = '/api/upload/avatar';
const uploadHeaders = {
  'Authorization': 'Bearer ' + (localStorage.getItem('token') || '')
};
const uploadData = computed(() => ({
  userId: formData.id || 'new'
}));

function handleAvatarUploadFinish({ file, event }: any) {
  try {
    const response = JSON.parse(event.target.response);
    if (response.success) {
      formData.avatar = response.data.url;
      message.success($t('page.userManagement.users.form.avatarUploadSuccess'));
    } else {
      message.error(response.message || $t('page.userManagement.users.form.avatarUploadError'));
    }
  } catch (error) {
    // 模拟上传成功（开发环境）
    const mockUrl = `/src/assets/imgs/avatars/user-${formData.id || Date.now()}.jpg`;
    formData.avatar = mockUrl;
    message.success($t('page.userManagement.users.form.avatarUploadSuccess'));
  }
}

function handleAvatarUploadError({ file, event }: any) {
  message.error($t('page.userManagement.users.form.avatarUploadError'));
}

// 下拉菜单操作处理
function handleDropdownAction(key: string, row: any) {
  switch (key) {
    case 'edit':
      handleEdit(row.id);
      break;
    case 'toggle-status':
      handleToggleStatus(row.id);
      break;
    case 'modify-balance':
      handleModifyBalance();
      break;
    case 'modify-points':
      handleModifyPoints();
      break;
    case 'gift-membership':
      handleGiftMembership();
      break;
    case 'set-group':
      handleSetGroup();
      break;
    case 'set-tags':
      handleSetTags();
      break;
    case 'set-promoter':
      handleSetPromoter();
      break;
    case 'delete':
      dialog.warning({
        title: $t('common.confirm'),
        content: $t('page.userManagement.users.confirmDelete'),
        positiveText: $t('common.confirm'),
        negativeText: $t('common.cancel'),
        onPositiveClick: () => {
          handleDelete(row.id);
        }
      });
      break;
  }
}



function handleToggleStatus(id: string) {
  const user = users.value.find(u => u.id === id);
  if (user) {
    user.status = user.status === 'active' ? 'disabled' : 'active';
    message.success($t('common.updateSuccess'));
  }
}



// 监听筛选条件变化
watch([searchQuery, selectedStatus, selectedLevel, selectedGroup], () => {
  pagination.value.page = 1;
  updatePagination();
});

// 监听语言变化，强制重新计算computed属性
watch(
  () => appStore.locale,
  () => {
    // 强制更新组件以重新计算国际化文本
    // 这里可以添加任何需要在语言切换时执行的逻辑
  }
);

// 初始化
updatePagination();
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ $t('page.userManagement.users.title') }}
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            {{ $t('page.userManagement.users.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" @click="handleAddUser">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ $t('page.userManagement.users.addUser') }}
          </NButton>
          <NButton @click="handleExport">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.userManagement.users.exportData') }}
          </NButton>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <NCard :bordered="false" class="card-wrapper">
        <NSpace vertical :size="16">
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:8">
              <NInput
                v-model="searchQuery"
                :placeholder="$t('page.userManagement.users.searchPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedStatus"
                :placeholder="$t('page.userManagement.users.selectStatus')"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedLevel"
                :placeholder="$t('page.userManagement.users.selectLevel')"
                :options="levelOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedGroup"
                :placeholder="$t('page.userManagement.users.selectGroup')"
                :options="groupOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:8">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  {{ $t('page.userManagement.users.search') }}
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ $t('page.userManagement.users.reset') }}
                </NButton>
                <NButton @click="toggleAdvancedFilter" :type="showAdvancedFilter ? 'primary' : 'default'">
                  <template #icon>
                    <SvgIcon :icon="showAdvancedFilter ? 'mdi:filter-minus' : 'mdi:filter-plus'" />
                  </template>
                  {{ $t('page.userManagement.users.advancedFilter') }}
                </NButton>
                <NButton @click="handleResetAllFilters">
                  <template #icon>
                    <SvgIcon icon="mdi:filter-remove" />
                  </template>
                  {{ $t('page.userManagement.users.resetFilter') }}
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>

          <!-- 高级筛选 -->
          <div v-show="showAdvancedFilter" class="border-t border-gray-200 dark:border-gray-700 pt-16px">
            <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedTags"
                  :placeholder="$t('page.userManagement.users.filter.selectTags')"
                  :options="tagsOptions"
                  multiple
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedIdentity"
                  :placeholder="$t('page.userManagement.users.filter.selectIdentity')"
                  :options="identityOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedMemberStatus"
                  :placeholder="$t('page.userManagement.users.filter.selectMemberStatus')"
                  :options="memberStatusOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedBalanceRange"
                  :placeholder="$t('page.userManagement.users.filter.selectBalanceRange')"
                  :options="balanceRangeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedPointsRange"
                  :placeholder="$t('page.userManagement.users.filter.selectPointsRange')"
                  :options="pointsRangeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedConsumptionTime"
                  :placeholder="$t('page.userManagement.users.filter.selectConsumptionTime')"
                  :options="consumptionTimeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedOrderRange"
                  :placeholder="$t('page.userManagement.users.filter.selectOrderRange')"
                  :options="orderRangeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedSpentRange"
                  :placeholder="$t('page.userManagement.users.filter.selectSpentRange')"
                  :options="spentRangeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedRechargeRange"
                  :placeholder="$t('page.userManagement.users.filter.selectRechargeRange')"
                  :options="rechargeRangeOptions"
                  clearable
                />
              </NGi>
              <NGi span="24 s:12 m:6">
                <NSelect
                  v-model="selectedLoginTime"
                  :placeholder="$t('page.userManagement.users.filter.selectLoginTime')"
                  :options="loginTimeOptions"
                  clearable
                />
              </NGi>
            </NGrid>
          </div>

          <!-- 批量操作 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-12px">
              <span class="text-14px text-gray-600">
                {{ $t('page.userManagement.users.selectedCount', { count: checkedRowKeys.length }) }}
              </span>
              <NButton
                size="small"
                type="success"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchEnable"
              >
                {{ $t('page.userManagement.users.batchEnable') }}
              </NButton>
              <NButton
                size="small"
                type="warning"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDisable"
              >
                {{ $t('page.userManagement.users.batchDisable') }}
              </NButton>
              <NButton
                size="small"
                type="error"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDelete"
              >
                {{ $t('page.userManagement.users.batchDelete') }}
              </NButton>
            </div>
            <div class="text-14px text-gray-600">
              {{ $t('page.userManagement.users.totalRecords', { total: filteredUsers.length }) }}
            </div>
          </div>
        </NSpace>
      </NCard>

      <!-- 用户列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="paginatedUsers"
          :row-key="(row: UserInfo) => row.id"
          :scroll-x="1400"
          flex-height
          style="min-height: 400px"
          class="responsive-table"
        />
        
        <div class="flex items-center justify-between mt-16px">
          <div class="text-14px text-gray-600">
            {{ $t('page.userManagement.users.showingRecords', {
              start: (pagination.page - 1) * pagination.pageSize + 1,
              end: Math.min(pagination.page * pagination.pageSize, pagination.total),
              total: pagination.total
            }) }}
          </div>
          <NPagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            show-size-picker
            show-quick-jumper
            @update:page="updatePagination"
            @update:page-size="updatePagination"
          />
        </div>
      </NCard>
    </NSpace>

    <!-- 用户表单弹窗 -->
    <NModal v-model:show="showUserModal" preset="dialog" :title="isEditMode ? $t('page.userManagement.users.editUser') : $t('page.userManagement.users.addUser')">
      <NForm ref="formRef" :model="formData" :rules="formRules" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <NGrid :x-gap="24" :y-gap="18" :cols="2">
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.username')" path="username">
              <NInput v-model:value="formData.username" :placeholder="$t('page.userManagement.users.form.usernamePlaceholder')" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.email')" path="email">
              <NInput v-model:value="formData.email" :placeholder="$t('page.userManagement.users.form.emailPlaceholder')" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.phone')" path="phone">
              <NInput v-model:value="formData.phone" :placeholder="$t('page.userManagement.users.form.phonePlaceholder')" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.status')" path="status">
              <NSelect v-model:value="formData.status" :options="statusOptions" />
            </NFormItem>
          </NGi>
          <NGi v-if="!isEditMode">
            <NFormItem :label="$t('page.userManagement.users.form.password')" path="password">
              <NInput v-model:value="formData.password" type="password" :placeholder="$t('page.userManagement.users.form.passwordPlaceholder')" />
            </NFormItem>
          </NGi>
          <NGi v-if="!isEditMode">
            <NFormItem :label="$t('page.userManagement.users.form.confirmPassword')" path="confirmPassword">
              <NInput v-model:value="formData.confirmPassword" type="password" :placeholder="$t('page.userManagement.users.form.confirmPasswordPlaceholder')" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.level')" path="level">
              <NSelect v-model:value="formData.level" :options="levelOptions" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.users.form.group')" path="group">
              <NSelect v-model:value="formData.group" :options="groupOptions" />
            </NFormItem>
          </NGi>
          <NGi span="2">
            <NFormItem :label="$t('page.userManagement.users.form.tags')" path="tags">
              <NDynamicTags v-model:value="formData.tags" />
            </NFormItem>
          </NGi>
          <NGi span="2">
            <NFormItem :label="$t('page.userManagement.users.form.uploadAvatar')">
              <div class="flex items-center space-x-16px">
                <NAvatar
                  :size="60"
                  :src="formData.avatar || `/src/assets/imgs/avatars/default-1.jpg`"
                  :fallback-src="`https://ui-avatars.com/api/?name=${formData.username || 'User'}&size=60&background=random`"
                />
                <NUpload
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :data="uploadData"
                  list-type="image"
                  :max="1"
                  accept="image/jpeg,image/jpg,image/png,image/webp"
                  @finish="handleAvatarUploadFinish"
                  @error="handleAvatarUploadError"
                >
                  <NButton size="small">{{ $t('page.userManagement.users.form.uploadAvatar') }}</NButton>
                </NUpload>
              </div>
              <div class="text-12px text-gray-500 mt-8px">
                {{ $t('page.userManagement.users.form.avatarUploadDesc') }}
              </div>
            </NFormItem>
          </NGi>
        </NGrid>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="handleCancelUser">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSaveUser">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 用户详情抽屉 -->
    <NDrawer v-model:show="showUserDetail" :width="drawerWidth" placement="right">
      <NDrawerContent :title="$t('page.userManagement.users.userDetail.title')" closable>
        <div v-if="currentUserDetail">
          <NTabs v-model:value="activeTab" type="line" animated class="user-detail-tabs">
            <!-- 用户信息选项卡 -->
            <NTabPane name="userInfo" :tab="$t('page.userManagement.users.userDetail.tabs.userInfo')">
              <div class="space-y-24px">
          <!-- 用户概览 -->
          <NCard :title="$t('page.userManagement.users.userDetail.overview.title')" size="small">
            <div class="flex items-center mb-16px">
              <NAvatar
                :size="80"
                :src="currentUserDetail.avatar || `/src/assets/imgs/avatars/default-${(parseInt(currentUserDetail.id) % 5) + 1}.jpg`"
                :fallback-src="`https://ui-avatars.com/api/?name=${currentUserDetail.username}&size=80&background=random`"
                class="mr-16px"
              />
              <div>
                <h3 class="text-18px font-medium mb-4px">{{ currentUserDetail.username }}</h3>
                <p class="text-14px text-gray-500">{{ currentUserDetail.email }}</p>
              </div>
            </div>
            <NGrid :x-gap="16" :y-gap="16" :cols="2">
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.balance')" :value="currentUserDetail.balance" :precision="2" />
              </NGi>
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.points')" :value="currentUserDetail.points" />
              </NGi>
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.totalOrders')" :value="currentUserDetail.orderCount" />
              </NGi>
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.totalSpent')" :value="currentUserDetail.totalSpent" :precision="2" />
              </NGi>
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.monthlyOrders')" :value="currentUserDetail.monthlyOrders" />
              </NGi>
              <NGi>
                <NStatistic :label="$t('page.userManagement.users.userDetail.overview.monthlySpent')" :value="currentUserDetail.monthlySpent" :precision="2" />
              </NGi>
            </NGrid>
          </NCard>

          <!-- 基本信息 -->
          <NCard :title="$t('page.userManagement.users.userDetail.basicInfo.title')" size="small">
            <NDescriptions :column="2" label-placement="left">
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.basicInfo.userId')">
                {{ currentUserDetail.id }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.basicInfo.realName')">
                {{ currentUserDetail.realName }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.columns.username')">
                {{ currentUserDetail.username }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.columns.email')">
                {{ currentUserDetail.email }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.columns.phone')">
                {{ currentUserDetail.phone }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.basicInfo.birthday')">
                {{ currentUserDetail.birthday }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.basicInfo.idCard')">
                {{ currentUserDetail.idCard }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.basicInfo.address')">
                {{ currentUserDetail.address }}
              </NDescriptionsItem>
            </NDescriptions>
          </NCard>

          <!-- 账户信息 -->
          <NCard :title="$t('page.userManagement.users.userDetail.accountInfo.title')" size="small">
            <NDescriptions :column="1" label-placement="left">
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.accountInfo.loginPassword')">
                {{ currentUserDetail.loginPassword }}
              </NDescriptionsItem>
            </NDescriptions>
          </NCard>

          <!-- 用户概况 -->
          <NCard :title="$t('page.userManagement.users.userDetail.userProfile.title')" size="small">
            <NDescriptions :column="2" label-placement="left">
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.promotionQualification')">
                {{ currentUserDetail.promotionQualification }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.userStatus')">
                <NTag :type="currentUserDetail.status === 'active' ? 'success' : 'error'" size="small">
                  {{ $t(`page.userManagement.users.status.${currentUserDetail.status}`) }}
                </NTag>
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.userLevel')">
                <NTag :type="currentUserDetail.level === 'vip' ? 'warning' : 'default'" size="small">
                  {{ $t(`page.userManagement.users.level.${currentUserDetail.level}`) }}
                </NTag>
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.userGroup')">
                {{ currentUserDetail.group }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.userTags')">
                <NSpace>
                  <NTag v-for="tag in currentUserDetail.tags" :key="tag" size="small" type="info">
                    {{ tag }}
                  </NTag>
                </NSpace>
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.promoter')">
                {{ currentUserDetail.promoter }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.registerTime')">
                {{ currentUserDetail.registerTime }}
              </NDescriptionsItem>
              <NDescriptionsItem :label="$t('page.userManagement.users.userDetail.userProfile.lastLoginTime')">
                {{ currentUserDetail.lastLoginTime || '从未登录' }}
              </NDescriptionsItem>
            </NDescriptions>
          </NCard>

          <!-- 用户备注 -->
          <NCard :title="$t('page.userManagement.users.userDetail.userNotes.title')" size="small">
            <NInput
              v-model="userNotes"
              type="textarea"
              :placeholder="$t('page.userManagement.users.userDetail.userNotes.placeholder')"
              :autosize="{ minRows: 3, maxRows: 6 }"
            />
            <div class="mt-12px">
              <NButton type="primary" size="small">
                {{ $t('page.userManagement.users.userDetail.userNotes.save') }}
              </NButton>
            </div>
          </NCard>

          <!-- 快捷操作 -->
          <NCard :title="$t('page.userManagement.users.userDetail.quickActions.title')" size="small">
            <NGrid :x-gap="12" :y-gap="12" :cols="4">
              <NGi>
                <NButton type="primary" size="small" block @click="handleModifyBalance">
                  {{ $t('page.userManagement.users.userDetail.quickActions.modifyBalance') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="warning" size="small" block @click="handleModifyPoints">
                  {{ $t('page.userManagement.users.userDetail.quickActions.modifyPoints') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="success" size="small" block @click="handleGiftCoupon">
                  {{ $t('page.userManagement.users.userDetail.quickActions.giftCoupon') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="info" size="small" block @click="handleGiftMembership">
                  {{ $t('page.userManagement.users.userDetail.quickActions.giftMembership') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="default" size="small" block @click="handleSetTags">
                  {{ $t('page.userManagement.users.userDetail.quickActions.setTags') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="default" size="small" block @click="handleSetGroup">
                  {{ $t('page.userManagement.users.userDetail.quickActions.setGroup') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="default" size="small" block @click="handleSetPromoter">
                  {{ $t('page.userManagement.users.userDetail.quickActions.setPromoter') }}
                </NButton>
              </NGi>
              <NGi>
                <NButton type="default" size="small" block @click="handleEditNotes">
                  {{ $t('page.userManagement.users.userDetail.quickActions.editNotes') }}
                </NButton>
              </NGi>
            </NGrid>
          </NCard>
              </div>
            </NTabPane>

            <!-- 消费记录选项卡 -->
            <NTabPane name="consumptionRecords" :tab="$t('page.userManagement.users.userDetail.tabs.consumptionRecords')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.consumptionRecords.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户的所有订单消费记录</p>
                </div>
                <NDataTable
                  :columns="consumptionColumns"
                  :data="currentUserDetail.consumptionRecords"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>

            <!-- 积分明细选项卡 -->
            <NTabPane name="pointsDetail" :tab="$t('page.userManagement.users.userDetail.tabs.pointsDetail')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.pointsDetail.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户积分获取和消费的详细记录</p>
                </div>
                <NDataTable
                  :columns="pointsColumns"
                  :data="currentUserDetail.pointsDetail"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>

            <!-- 签到记录选项卡 -->
            <NTabPane name="signInRecords" :tab="$t('page.userManagement.users.userDetail.tabs.signInRecords')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.signInRecords.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户签到历史和获得的积分奖励</p>
                </div>
                <NDataTable
                  :columns="signInColumns"
                  :data="currentUserDetail.signInRecords"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>

            <!-- 持有优惠券选项卡 -->
            <NTabPane name="coupons" :tab="$t('page.userManagement.users.userDetail.tabs.coupons')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.coupons.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户当前拥有的所有优惠券</p>
                </div>
                <NDataTable
                  :columns="couponsColumns"
                  :data="currentUserDetail.coupons"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>

            <!-- 余额变动选项卡 -->
            <NTabPane name="balanceChanges" :tab="$t('page.userManagement.users.userDetail.tabs.balanceChanges')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.balanceChanges.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户余额的充值、消费、退款记录</p>
                </div>
                <NDataTable
                  :columns="balanceColumns"
                  :data="currentUserDetail.balanceChanges"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>

            <!-- 好友关系选项卡 -->
            <NTabPane name="friendRelations" :tab="$t('page.userManagement.users.userDetail.tabs.friendRelations')">
              <div class="p-16px">
                <div class="mb-16px">
                  <h3 class="text-16px font-medium mb-8px">{{ $t('page.userManagement.users.userDetail.friendRelations.title') }}</h3>
                  <p class="text-14px text-gray-500">查看用户的好友列表和推广关系</p>
                </div>
                <NDataTable
                  :columns="friendsColumns"
                  :data="currentUserDetail.friendRelations"
                  :pagination="{ pageSize: 10 }"
                  size="small"
                />
              </div>
            </NTabPane>
          </NTabs>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
/* 必要的组件深度样式，UnoCSS无法完全替代的部分 */
.user-detail-tabs :deep(.n-tabs-content) {
  padding-top: 16px;
}

.user-detail-tabs :deep(.n-tab-pane) {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .user-detail-tabs :deep(.n-tab-pane) {
    height: calc(100vh - 180px);
  }
}

@media (max-width: 768px) {
  .user-detail-tabs :deep(.n-tabs-nav) {
    overflow-x: auto;
    white-space: nowrap;
  }

  .user-detail-tabs :deep(.n-tab-pane) {
    height: calc(100vh - 120px);
  }
}
</style>
