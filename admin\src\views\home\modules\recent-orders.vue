<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'RecentOrders'
});

interface OrderItem {
  id: string;
  orderNo: string;
  customerName: string;
  amount: number;
  status: 'pending' | 'paid' | 'shipped' | 'completed' | 'cancelled';
  createTime: string;
}

const recentOrders = computed<OrderItem[]>(() => [
  {
    id: '1',
    orderNo: 'FS202501010001',
    customerName: $t('page.home.sampleCustomer1'),
    amount: 299.00,
    status: 'paid',
    createTime: '2025-01-27 10:30:00'
  },
  {
    id: '2',
    orderNo: 'FS202501010002',
    customerName: $t('page.home.sampleCustomer2'),
    amount: 1299.00,
    status: 'shipped',
    createTime: '2025-01-27 09:15:00'
  },
  {
    id: '3',
    orderNo: 'FS202501010003',
    customerName: $t('page.home.sampleCustomer3'),
    amount: 599.00,
    status: 'pending',
    createTime: '2025-01-27 08:45:00'
  },
  {
    id: '4',
    orderNo: 'FS202501010004',
    customerName: $t('page.home.sampleCustomer4'),
    amount: 899.00,
    status: 'completed',
    createTime: '2025-01-26 16:20:00'
  },
  {
    id: '5',
    orderNo: 'FS202501010005',
    customerName: $t('page.home.sampleCustomer5'),
    amount: 199.00,
    status: 'cancelled',
    createTime: '2025-01-26 14:10:00'
  }
]);

const statusConfig = computed(() => ({
  pending: {
    label: $t('page.home.orderStatus.pending'),
    type: 'warning' as const,
    icon: 'mdi:clock-outline'
  },
  paid: {
    label: $t('page.home.orderStatus.paid'),
    type: 'info' as const,
    icon: 'mdi:credit-card-check'
  },
  shipped: {
    label: $t('page.home.orderStatus.shipped'),
    type: 'primary' as const,
    icon: 'mdi:truck-delivery'
  },
  completed: {
    label: $t('page.home.orderStatus.completed'),
    type: 'success' as const,
    icon: 'mdi:check-circle'
  },
  cancelled: {
    label: $t('page.home.orderStatus.cancelled'),
    type: 'error' as const,
    icon: 'mdi:close-circle'
  }
}));

function getStatusConfig(status: OrderItem['status']) {
  return statusConfig.value[status];
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <SvgIcon icon="mdi:format-list-bulleted" class="text-20px mr-8px text-primary" />
          <span class="text-16px font-medium">{{ $t('page.home.recentOrders') }}</span>
        </div>
        <NButton text type="primary" size="small">
          {{ $t('page.home.viewAll') }}
          <template #icon>
            <SvgIcon icon="mdi:arrow-right" />
          </template>
        </NButton>
      </div>
    </template>

    <div class="space-y-12px">
      <div 
        v-for="order in recentOrders" 
        :key="order.id"
        class="flex items-center justify-between p-12px bg-gray-50 dark:bg-gray-800 rd-8px hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <div class="flex items-center space-x-12px flex-1">
          <div class="flex-shrink-0">
            <SvgIcon 
              :icon="getStatusConfig(order.status).icon" 
              class="text-20px"
              :class="`text-${getStatusConfig(order.status).type}`"
            />
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-8px mb-4px">
              <span class="text-14px font-medium text-gray-900 dark:text-gray-100">
                {{ order.orderNo }}
              </span>
              <NTag 
                :type="getStatusConfig(order.status).type" 
                size="small"
                round
              >
                {{ getStatusConfig(order.status).label }}
              </NTag>
            </div>
            <div class="text-12px text-gray-500 dark:text-gray-400">
              {{ order.customerName }} • {{ order.createTime }}
            </div>
          </div>
        </div>
        <div class="text-right flex-shrink-0">
          <div class="text-16px font-bold text-primary">
            ¥{{ order.amount.toFixed(2) }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <NButton type="primary" ghost>
          {{ $t('page.home.viewMoreOrders') }}
        </NButton>
      </div>
    </template>
  </NCard>
</template>

<style scoped></style>
