<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h, watch, reactive, nextTick } from 'vue';
import type { DataTableColumns, DataTableRowKey, FormInst, FormRules } from 'naive-ui';
import {
  NButton,
  NTag,
  NPopconfirm,
  NDropdown,
  useMessage,
  useDialog,
  NColorPicker,
  NCheckboxGroup,
  NCheckbox,
  NSpace,
  NCard,
  NGrid,
  NGi,
  NInput,
  NSelect,
  NDataTable,
  NPagination,
  NModal,
  NForm,
  NFormItem,
  NDrawer,
  NDrawerContent,
  NTabs,
  NTabPane,
  NText
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { getModalClass, useDrawerWidth, getRecommendedModalSize } from '@/utils/modal';

defineOptions({
  name: 'UserGroupManagement'
});

const appStore = useAppStore();
const message = useMessage();
const dialog = useDialog();

interface GroupInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  permissions: string[];
  userCount: number;
  status: 'active' | 'disabled';
  createdAt: string;
  updatedAt: string;
}

// 模拟分组数据
const groups = ref<GroupInfo[]>([
  {
    id: '1',
    name: '管理员',
    description: '系统管理员，拥有所有权限',
    color: '#ff6b6b',
    permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
    userCount: 5,
    status: 'active',
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-15 14:30:00'
  },
  {
    id: '2',
    name: 'VIP用户',
    description: 'VIP会员，享受特殊权限和优惠',
    color: '#4ecdc4',
    permissions: ['browse', 'purchase', 'comment', 'vipDiscount', 'prioritySupport', 'exclusiveProducts'],
    userCount: 128,
    status: 'active',
    createdAt: '2024-01-02 09:15:00',
    updatedAt: '2024-01-16 11:20:00'
  },
  {
    id: '3',
    name: '普通用户',
    description: '普通用户分组，基础权限',
    color: '#45b7d1',
    permissions: ['browse', 'purchase', 'comment'],
    userCount: 1024,
    status: 'active',
    createdAt: '2024-01-03 16:45:00',
    updatedAt: '2024-01-17 09:10:00'
  },
  {
    id: '4',
    name: '商家用户',
    description: '商家用户，可以发布和管理商品',
    color: '#96ceb4',
    permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
    userCount: 45,
    status: 'active',
    createdAt: '2024-01-04 13:20:00',
    updatedAt: '2024-01-18 15:45:00'
  },
  {
    id: '5',
    name: '测试分组',
    description: '用于测试的分组',
    color: '#feca57',
    permissions: ['browse'],
    userCount: 0,
    status: 'disabled',
    createdAt: '2024-01-05 08:30:00',
    updatedAt: '2024-01-19 12:15:00'
  }
]);

// 搜索和筛选状态
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);
const showAdvancedFilter = ref(false);
const selectedPermissions = ref<string[]>([]);

// 状态选项
const statusOptions = computed(() => [
  { label: $t('page.userManagement.groups.status.active'), value: 'active' },
  { label: $t('page.userManagement.groups.status.disabled'), value: 'disabled' }
]);

// 权限选项
const permissionOptions = computed(() => [
  { label: $t('page.userManagement.groups.permissions.browse'), value: 'browse' },
  { label: $t('page.userManagement.groups.permissions.purchase'), value: 'purchase' },
  { label: $t('page.userManagement.groups.permissions.comment'), value: 'comment' },
  { label: $t('page.userManagement.groups.permissions.vipDiscount'), value: 'vipDiscount' },
  { label: $t('page.userManagement.groups.permissions.prioritySupport'), value: 'prioritySupport' },
  { label: $t('page.userManagement.groups.permissions.exclusiveProducts'), value: 'exclusiveProducts' },
  { label: $t('page.userManagement.groups.permissions.merchantManage'), value: 'merchantManage' },
  { label: $t('page.userManagement.groups.permissions.productPublish'), value: 'productPublish' }
]);

// 筛选后的数据
const filteredGroups = computed(() => {
  let result = groups.value;

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(group =>
      group.name.toLowerCase().includes(query) ||
      group.description.toLowerCase().includes(query)
    );
  }

  // 状态筛选
  if (selectedStatus.value) {
    result = result.filter(group => group.status === selectedStatus.value);
  }

  // 权限筛选
  if (selectedPermissions.value.length > 0) {
    result = result.filter(group =>
      selectedPermissions.value.some(permission => group.permissions.includes(permission))
    );
  }

  return result;
});

// 分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0
});

// 分页数据
const paginatedGroups = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredGroups.value.slice(start, end);
});

// 更新分页信息
function updatePagination() {
  pagination.value.total = filteredGroups.value.length;
  const maxPage = Math.ceil(pagination.value.total / pagination.value.pageSize);
  if (pagination.value.page > maxPage && maxPage > 0) {
    pagination.value.page = maxPage;
  }
}

// 选中的行
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 表单相关状态
const showGroupModal = ref(false);
const isEditMode = ref(false);
const formRef = ref<FormInst | null>(null);

// 分组详情抽屉状态
const showGroupDetail = ref(false);
const currentGroupId = ref('');
const activeTab = ref('groupInfo');

// 响应式抽屉宽度
const drawerWidth = useDrawerWidth('large');

// 当前分组详情
const currentGroupDetail = computed(() => {
  return groups.value.find(group => group.id === currentGroupId.value);
});

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [] as string[],
  status: 'active' as 'active' | 'disabled'
});

// 表单验证规则
const formRules = computed<FormRules>(() => ({
  name: [
    {
      required: true,
      message: $t('page.userManagement.groups.nameRequired'),
      trigger: ['blur', 'input'],
      validator: (rule: any, value: string) => {
        if (!value || value.trim() === '') {
          return new Error($t('page.userManagement.groups.nameRequired'));
        }
        if (value.trim().length < 2) {
          return new Error('分组名称不能少于2个字符');
        }
        if (value.trim().length > 50) {
          return new Error('分组名称不能超过50个字符');
        }
        return true;
      }
    }
  ],
  description: [
    {
      validator: (rule: any, value: string) => {
        if (value && value.length > 500) {
          return new Error('分组描述不能超过500个字符');
        }
        return true;
      },
      trigger: ['blur', 'input']
    }
  ],
  permissions: [
    {
      validator: (rule: any, value: string[]) => {
        if (!value || !Array.isArray(value) || value.length === 0) {
          return new Error('请至少选择一项权限');
        }
        return true;
      },
      trigger: ['blur', 'change']
    }
  ]
}));

// 重置表单
function resetForm() {
  formData.id = '';
  formData.name = '';
  formData.description = '';
  formData.color = '#4ecdc4';
  formData.permissions = [];
  formData.status = 'active';

  // 清除表单验证状态
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
}

// 分组名称输入处理
function handleNameInput(value: string) {
  formData.name = value;
  // 实时输入时不触发验证，避免过度验证
}

// 分组名称失焦处理
function handleNameBlur() {
  // 失焦时也不立即验证，统一在提交时验证
}

function toggleAdvancedFilter() {
  showAdvancedFilter.value = !showAdvancedFilter.value;
}



// ==================== 权限模块相关逻辑 ====================

// 权限选择统计计算属性
const selectedPermissionsCount = computed(() => {
  return Array.isArray(formData.permissions) ? formData.permissions.length : 0;
});

const totalPermissionsCount = computed(() => {
  return permissionOptions.value ? permissionOptions.value.length : 0;
});

// 权限选择统计文本
const permissionsStatsText = computed(() => {
  const selected = selectedPermissionsCount.value;
  const total = totalPermissionsCount.value;
  return `已选择 ${selected} / ${total} 项权限`;
});

// 权限变化处理函数
function handlePermissionsChange(value: string[]) {
  formData.permissions = value;

  // 不需要立即验证，让表单在提交时统一验证
  // 这里只是更新数据，避免触发不必要的验证错误
}

// 权限标签获取函数
function getPermissionLabel(value: string): string {
  const option = permissionOptions.value.find(opt => opt.value === value);
  return option ? option.label : value;
}

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: $t('page.userManagement.groups.table.name'),
    key: 'name',
    width: 150,
    render: (row: GroupInfo) => {
      return h('div', { class: 'flex items-center space-x-8px' }, [
        h('div', {
          class: 'w-12px h-12px rounded-full',
          style: { backgroundColor: row.color }
        }),
        h('span', { class: 'font-medium' }, row.name)
      ]);
    }
  },
  {
    title: $t('page.userManagement.groups.table.description'),
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('page.userManagement.groups.table.permissions'),
    key: 'permissions',
    width: 200,
    render: (row: GroupInfo) => {
      if (row.permissions.length === 0) {
        return h(NTag, { type: 'default', size: 'small' }, { default: () => $t('page.userManagement.groups.noPermissions') });
      }
      return h('div', { class: 'flex flex-wrap gap-4px' },
        row.permissions.slice(0, 3).map(permission =>
          h(NTag, { type: 'info', size: 'small' }, { default: () => getPermissionLabel(permission) })
        ).concat(
          row.permissions.length > 3 ? [
            h(NTag, { type: 'default', size: 'small' }, { default: () => `+${row.permissions.length - 3}` })
          ] : []
        )
      );
    }
  },
  {
    title: $t('page.userManagement.groups.table.userCount'),
    key: 'userCount',
    width: 120,
    render: (row: GroupInfo) => row.userCount.toString()
  },
  {
    title: $t('page.userManagement.groups.table.status'),
    key: 'status',
    width: 100,
    render: (row: GroupInfo) => {
      const statusMap = {
        active: { type: 'success' as const, label: $t('page.userManagement.groups.status.active') },
        disabled: { type: 'error' as const, label: $t('page.userManagement.groups.status.disabled') }
      };
      const config = statusMap[row.status] || { type: 'default' as const, label: row.status };
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: $t('page.userManagement.groups.table.updateTime'),
    key: 'updatedAt',
    width: 150,
    render: (row: GroupInfo) => new Date(row.updatedAt).toLocaleDateString()
  },
  {
    title: $t('page.userManagement.groups.table.actions'),
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: GroupInfo) => {
      const options = [
        {
          label: $t('page.userManagement.groups.clone'),
          key: 'clone',
          icon: () => h(SvgIcon, { icon: 'mdi:content-copy' })
        },
        {
          label: $t('page.userManagement.groups.exportMembers'),
          key: 'exportMembers',
          icon: () => h(SvgIcon, { icon: 'mdi:download' })
        },
        {
          type: 'divider'
        },
        {
          label: row.status === 'active' ? $t('page.userManagement.groups.disable') : $t('page.userManagement.groups.enable'),
          key: 'toggleStatus',
          icon: () => h(SvgIcon, { icon: row.status === 'active' ? 'mdi:pause' : 'mdi:play' })
        },
        {
          label: $t('page.userManagement.groups.delete'),
          key: 'delete',
          icon: () => h(SvgIcon, { icon: 'mdi:delete' }),
          disabled: row.userCount > 0
        }
      ];

      return h('div', { class: 'flex items-center space-x-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          onClick: () => handleViewMembers(row.id)
        }, { default: () => $t('page.userManagement.groups.viewMembers') }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          text: true,
          onClick: () => handleEdit(row.id)
        }, { default: () => $t('page.userManagement.groups.edit') }),
        h(NDropdown, {
          options,
          onSelect: (key: string) => handleDropdownSelect(key, row)
        }, {
          default: () => h(NButton, {
            size: 'small',
            text: true
          }, { default: () => $t('common.more') })
        })
      ]);
    }
  }
] as DataTableColumns<GroupInfo>);

// 操作函数
function handleSearch() {
  pagination.value.page = 1;
  updatePagination();
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  selectedPermissions.value = [];
  pagination.value.page = 1;
  updatePagination();
}

function handleViewMembers(id: string) {
  currentGroupId.value = id;
  activeTab.value = 'groupInfo';
  showGroupDetail.value = true;
}

function handleEdit(id: string) {
  handleEditGroup(id);
}

function handleDropdownSelect(key: string, row: GroupInfo) {
  switch (key) {
    case 'clone':
      handleCloneGroup(row.id);
      break;
    case 'exportMembers':
      handleExportMembers(row.id);
      break;
    case 'toggleStatus':
      handleToggleStatus(row.id);
      break;
    case 'delete':
      handleDelete(row.id);
      break;
  }
}

function handleToggleStatus(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    group.status = group.status === 'active' ? 'disabled' : 'active';
    message.success($t('common.updateSuccess'));
  }
}

function handleDelete(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group && group.userCount > 0) {
    message.warning($t('page.userManagement.groups.cannotDeleteWithUsers'));
    return;
  }

  dialog.warning({
    title: $t('page.userManagement.groups.delete'),
    content: $t('page.userManagement.groups.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      const index = groups.value.findIndex(group => group.id === id);
      if (index > -1) {
        groups.value.splice(index, 1);
        message.success($t('page.userManagement.groups.deleteSuccess'));
        updatePagination();
      }
    }
  });
}

function handleBatchEnable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.groups.selectGroups'));
    return;
  }
  checkedRowKeys.value.forEach(id => {
    const group = groups.value.find(g => g.id === id);
    if (group) {
      group.status = 'active';
    }
  });
  message.success($t('page.userManagement.groups.batchEnableSuccess'));
}

function handleBatchDisable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.groups.selectGroups'));
    return;
  }
  checkedRowKeys.value.forEach(id => {
    const group = groups.value.find(g => g.id === id);
    if (group) {
      group.status = 'disabled';
    }
  });
  message.success($t('page.userManagement.groups.batchDisableSuccess'));
}

function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    message.warning($t('page.userManagement.groups.selectGroups'));
    return;
  }

  // 检查是否有分组包含用户
  const hasUsersGroups = checkedRowKeys.value.some(id => {
    const group = groups.value.find(g => g.id === id);
    return group && group.userCount > 0;
  });

  if (hasUsersGroups) {
    message.warning($t('page.userManagement.groups.cannotDeleteWithUsers'));
    return;
  }

  dialog.warning({
    title: $t('page.userManagement.groups.batchDelete'),
    content: $t('page.userManagement.groups.confirmBatchDelete', { count: checkedRowKeys.value.length }),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      checkedRowKeys.value.forEach(id => {
        const index = groups.value.findIndex(group => group.id === id);
        if (index > -1) {
          groups.value.splice(index, 1);
        }
      });
      checkedRowKeys.value = [];
      message.success($t('page.userManagement.groups.batchDeleteSuccess'));
      updatePagination();
    }
  });
}

function handleAddGroup() {
  isEditMode.value = false;
  resetForm();
  showGroupModal.value = true;
}

function handleEditGroup(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    // 设置编辑模式
    isEditMode.value = true;

    // 直接回填表单数据，不要先重置
    formData.id = group.id;
    formData.name = group.name;
    formData.description = group.description || '';
    formData.color = group.color || '#4ecdc4';
    formData.permissions = [...group.permissions];
    formData.status = group.status || 'active';

    // 清除表单验证状态
    if (formRef.value) {
      formRef.value.restoreValidation();
    }

    // 显示表单弹窗
    showGroupModal.value = true;
  }
}

function handleCloneGroup(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    // 先重置表单状态
    resetForm();

    // 设置为新增模式
    isEditMode.value = false;

    // 回填表单数据（克隆原分组信息）
    formData.id = '';
    formData.name = `${group.name} - 副本`;
    formData.description = group.description || '';
    formData.color = group.color || '#4ecdc4';
    formData.permissions = [...group.permissions];
    formData.status = group.status || 'active';

    // 显示表单弹窗
    showGroupModal.value = true;

    // 确保表单验证状态正确
    nextTick(() => {
      if (formRef.value) {
        formRef.value.restoreValidation();
      }
    });
  }
}

function handleExport() {
  message.info($t('page.userManagement.groups.exportDataSuccess' as any));
}

function handleExportMembers(id: string) {
  const group = groups.value.find(g => g.id === id);
  if (group) {
    message.info($t('page.userManagement.groups.exportMembersSuccess' as any) + `: ${group.name}`);
  }
}

function handleSubmitGroup() {
  if (!formRef.value) {
    message.error('表单初始化失败，请刷新页面重试');
    return;
  }

  formRef.value.validate((errors) => {
    if (!errors) {
      try {
        if (isEditMode.value) {
          // 编辑分组
          const group = groups.value.find(g => g.id === formData.id);
          if (group) {
            group.name = formData.name;
            group.description = formData.description;
            group.color = formData.color;
            group.permissions = [...formData.permissions];
            group.status = formData.status;
            group.updatedAt = new Date().toLocaleString();
            message.success($t('page.userManagement.groups.updateSuccess'));
          }
        } else {
          // 新增分组
          const newGroup: GroupInfo = {
            id: Date.now().toString(),
            name: formData.name,
            description: formData.description,
            color: formData.color,
            permissions: [...formData.permissions],
            userCount: 0,
            status: formData.status,
            createdAt: new Date().toLocaleString(),
            updatedAt: new Date().toLocaleString()
          };
          groups.value.unshift(newGroup);
          message.success($t('page.userManagement.groups.createSuccess'));
        }
        showGroupModal.value = false;
        updatePagination();
      } catch (error) {
        message.error('操作失败，请重试');
      }
    } else {
      // 处理验证错误信息
      if (Array.isArray(errors) && errors.length > 0) {
        const firstError = errors[0];
        if (Array.isArray(firstError) && firstError.length > 0) {
          message.error(firstError[0].message || '表单填写有误，请检查');
        } else {
          message.error('请检查表单填写是否正确');
        }
      } else {
        message.error('表单验证失败，请检查填写内容');
      }
    }
  });
}

function handleCancelGroup() {
  showGroupModal.value = false;
  resetForm();
}

// 监听筛选条件变化
watch([searchQuery, selectedStatus, selectedPermissions], () => {
  pagination.value.page = 1;
  updatePagination();
});

// 监听语言变化，强制重新计算computed属性
watch(
  () => appStore.locale,
  () => {
    // 强制更新组件以重新计算国际化文本
  }
);

// 初始化
updatePagination();
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ $t('page.userManagement.groups.title') }}
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            {{ $t('page.userManagement.groups.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" @click="handleAddGroup">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ $t('page.userManagement.groups.createGroup') }}
          </NButton>
          <NButton @click="handleExport">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.userManagement.groups.exportData') }}
          </NButton>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <NCard :bordered="false" class="card-wrapper">
        <NSpace vertical :size="16">
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:8">
              <NInput
                v-model="searchQuery"
                :placeholder="$t('page.userManagement.groups.searchPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedStatus"
                :placeholder="$t('page.userManagement.groups.selectStatus')"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:8">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  {{ $t('page.userManagement.groups.search') }}
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ $t('page.userManagement.groups.reset') }}
                </NButton>
                <NButton @click="toggleAdvancedFilter" :type="showAdvancedFilter ? 'primary' : 'default'">
                  <template #icon>
                    <SvgIcon :icon="showAdvancedFilter ? 'mdi:filter-minus' : 'mdi:filter-plus'" />
                  </template>
                  {{ $t('page.userManagement.groups.advancedFilter') }}
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>

          <!-- 高级筛选 -->
          <div v-show="showAdvancedFilter" class="border-t border-gray-200 dark:border-gray-700 pt-16px">
            <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
              <NGi span="24 s:12 m:8">
                <NCheckboxGroup v-model="selectedPermissions">
                  <NSpace>
                    <NCheckbox
                      v-for="option in permissionOptions"
                      :key="option.value"
                      :value="option.value"
                      :label="option.label"
                    />
                  </NSpace>
                </NCheckboxGroup>
              </NGi>
            </NGrid>
          </div>

          <!-- 批量操作 -->
          <div v-if="checkedRowKeys.length > 0" class="flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 p-12px rounded-8px">
            <div class="text-14px text-gray-600">
              {{ $t('page.userManagement.groups.selectedCount', { count: checkedRowKeys.length }) }}
            </div>
            <NSpace>
              <NButton size="small" @click="handleBatchEnable">
                {{ $t('page.userManagement.groups.batchEnable') }}
              </NButton>
              <NButton size="small" @click="handleBatchDisable">
                {{ $t('page.userManagement.groups.batchDisable') }}
              </NButton>
              <NPopconfirm @positive-click="handleBatchDelete">
                <template #trigger>
                  <NButton size="small" type="error">
                    {{ $t('page.userManagement.groups.batchDelete') }}
                  </NButton>
                </template>
                {{ $t('page.userManagement.groups.confirmBatchDelete', { count: checkedRowKeys.length }) }}
              </NPopconfirm>
            </NSpace>
          </div>

          <!-- 统计信息 -->
          <div class="flex items-center justify-between">
            <div class="text-14px text-gray-600">
              {{ $t('page.userManagement.groups.totalCount', { total: filteredGroups.length }) }}
            </div>
          </div>
        </NSpace>
      </NCard>

      <!-- 分组列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="paginatedGroups"
          :row-key="(row) => row.id"
          :scroll-x="1400"
          flex-height
          style="min-height: 400px"
          class="responsive-table"
        />

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-16px">
          <div class="text-14px text-gray-600">
            {{ $t('page.userManagement.groups.showingRecords', {
              start: (pagination.page - 1) * pagination.pageSize + 1,
              end: Math.min(pagination.page * pagination.pageSize, pagination.total),
              total: pagination.total
            }) }}
          </div>
          <NPagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            show-size-picker
            show-quick-jumper
            @update:page="updatePagination"
            @update:page-size="updatePagination"
          />
        </div>
      </NCard>
    </NSpace>

    <!-- 分组表单弹窗 -->
    <NModal
      v-model:show="showGroupModal"
      preset="dialog"
      :title="isEditMode ? $t('page.userManagement.groups.editGroup') : $t('page.userManagement.groups.createGroup')"
      class="modal-group-form modal-form"
      @mask-click="handleCancelGroup"
      @esc="handleCancelGroup"
    >
      <NForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-placement="left"
        label-width="140px"
        require-mark-placement="right-hanging"
        size="medium"
      >
        <NGrid :x-gap="30" :y-gap="26" :cols="2" responsive="screen" :collapsed-rows="1">
          <!-- 第一行：分组名称和分组颜色 -->
          <NGi>
            <NFormItem :label="$t('page.userManagement.groups.form.name')" path="name">
              <NInput
                v-model="formData.name"
                :placeholder="$t('page.userManagement.groups.form.namePlaceholder')"
                clearable
                size="medium"
                maxlength="50"
                show-count
                @input="handleNameInput"
                @blur="handleNameBlur"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.groups.form.color')" path="color">
              <NColorPicker
                v-model="formData.color"
                :modes="['hex']"
                size="medium"
                :show-alpha="false"
              />
            </NFormItem>
          </NGi>
          <!-- 第二行：分组描述（跨两列） -->
          <NGi :span="2">
            <NFormItem :label="$t('page.userManagement.groups.form.description')" path="description">
              <NInput
                v-model="formData.description"
                type="textarea"
                :placeholder="$t('page.userManagement.groups.form.descriptionPlaceholder')"
                :autosize="{ minRows: 4, maxRows: 8 }"
                size="medium"
                maxlength="500"
                show-count
                clearable
              />
            </NFormItem>
          </NGi>
          <!-- 分组权限模块（跨两列） -->
          <NGi :span="2">
            <NFormItem :label="$t('page.userManagement.groups.form.permissions')" path="permissions">
              <!-- 权限模块容器 -->
              <div class="group-permissions-module">

                <!-- 权限描述 -->
                <div class="permissions-description">
                  {{ $t('page.userManagement.groups.form.permissionsDesc') }}
                </div>

                <!-- 权限选择区域 -->
                <div class="permissions-selection-area">
                  <NCheckboxGroup v-model="formData.permissions" @update:value="handlePermissionsChange">
                    <div class="permissions-grid">
                      <div
                        v-for="option in permissionOptions"
                        :key="option.value"
                        class="permission-item"
                      >
                        <NCheckbox
                          :value="option.value"
                          :label="option.label"
                          size="medium"
                        />
                      </div>
                    </div>
                  </NCheckboxGroup>
                </div>

                <!-- 权限统计和预览 -->
                <div class="permissions-summary">
                  <!-- 统计信息 -->
                  <div class="permissions-stats">
                    <span class="stats-text">{{ permissionsStatsText }}</span>
                    <span v-if="selectedPermissionsCount > 0" class="stats-hint">
                      {{ selectedPermissionsCount === 1 ? '已选择1项权限' : `已选择${selectedPermissionsCount}项权限` }}
                    </span>
                  </div>

                  <!-- 权限预览 -->
                  <div v-if="selectedPermissionsCount > 0" class="permissions-preview">
                    <div class="preview-tags">
                      <NTag
                        v-for="permission in formData.permissions.slice(0, 6)"
                        :key="permission"
                        size="small"
                        type="info"
                        round
                      >
                        {{ getPermissionLabel(permission) }}
                      </NTag>
                      <NTag
                        v-if="formData.permissions.length > 6"
                        size="small"
                        type="default"
                        round
                      >
                        +{{ formData.permissions.length - 6 }}更多
                      </NTag>
                    </div>
                  </div>
                </div>

              </div>
            </NFormItem>
          </NGi>
        </NGrid>
      </NForm>

      <template #action>
        <div class="flex justify-end items-center space-x-16px pt-20px">
          <NButton
            size="medium"
            @click="handleCancelGroup"
            class="min-w-100px"
          >
            {{ $t('page.userManagement.groups.form.cancel') }}
          </NButton>
          <NButton
            type="primary"
            size="medium"
            @click="handleSubmitGroup"
            class="min-w-100px"
            :loading="false"
          >
            {{ $t('page.userManagement.groups.form.save') }}
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 分组详情抽屉 -->
    <NDrawer v-model="showGroupDetail" :width="drawerWidth" placement="right">
      <NDrawerContent :title="$t('page.userManagement.groups.drawer.membersTitle')" closable>
        <div v-if="currentGroupDetail">
          <NTabs v-model="activeTab" type="line" animated class="group-detail-tabs">
            <!-- 分组信息选项卡 -->
            <NTabPane name="groupInfo" :tab="$t('page.userManagement.groups.table.name')">
              <div class="space-y-24px">
                <!-- 分组概览 -->
                <NCard :title="$t('page.userManagement.groups.table.name')" size="small">
                  <div class="flex items-center mb-16px">
                    <div
                      class="w-40px h-40px rounded-full mr-16px"
                      :style="{ backgroundColor: currentGroupDetail.color }"
                    />
                    <div>
                      <h3 class="text-18px font-medium mb-4px">{{ currentGroupDetail.name }}</h3>
                      <p class="text-14px text-gray-500">{{ currentGroupDetail.description }}</p>
                    </div>
                  </div>

                  <NGrid :x-gap="16" :y-gap="16" :cols="2">
                    <NGi>
                      <div class="text-center p-16px bg-gray-50 dark:bg-gray-800 rounded-8px">
                        <div class="text-24px font-bold text-blue-600">{{ currentGroupDetail.userCount }}</div>
                        <div class="text-14px text-gray-500 mt-4px">{{ $t('page.userManagement.groups.drawer.memberCount') }}</div>
                      </div>
                    </NGi>
                    <NGi>
                      <div class="text-center p-16px bg-gray-50 dark:bg-gray-800 rounded-8px">
                        <div class="text-24px font-bold text-green-600">{{ currentGroupDetail.permissions.length }}</div>
                        <div class="text-14px text-gray-500 mt-4px">{{ $t('page.userManagement.groups.table.permissions') }}</div>
                      </div>
                    </NGi>
                  </NGrid>
                </NCard>

                <!-- 权限详情 -->
                <NCard :title="$t('page.userManagement.groups.table.permissions')" size="small">
                  <div v-if="currentGroupDetail.permissions.length === 0" class="text-center py-32px text-gray-500">
                    {{ $t('page.userManagement.groups.noPermissions') }}
                  </div>
                  <div v-else class="flex flex-wrap gap-8px">
                    <NTag
                      v-for="permission in currentGroupDetail.permissions"
                      :key="permission"
                      type="info"
                      size="medium"
                    >
                      {{ permissionOptions.find(p => p.value === permission)?.label || permission }}
                    </NTag>
                  </div>
                </NCard>
              </div>
            </NTabPane>
          </NTabs>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
/* 必要的组件深度样式，UnoCSS无法完全替代的部分 */
.group-detail-tabs :deep(.n-tabs-content) {
  padding-top: 16px;
}

.group-detail-tabs :deep(.n-tab-pane) {
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.responsive-table :deep(.n-data-table-th) {
  white-space: nowrap;
}

.responsive-table :deep(.n-data-table-td) {
  white-space: nowrap;
}

@media (max-width: 768px) {
  .responsive-table :deep(.n-data-table) {
    font-size: 12px;
  }
}
</style>