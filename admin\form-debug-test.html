<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组表单数据回填调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .debug-item {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: white;
            border-radius: 4px;
        }
        .success {
            border-left-color: #28a745;
        }
        .warning {
            border-left-color: #ffc107;
        }
        .error {
            border-left-color: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 12px;
        }
        h1, h2, h3 {
            color: #333;
        }
        h1 {
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        .icon {
            font-size: 18px;
            margin-right: 8px;
        }
        .success-icon { color: #28a745; }
        .warning-icon { color: #ffc107; }
        .error-icon { color: #dc3545; }
        .info-icon { color: #007bff; }
        .debug-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .debug-summary h3 {
            color: white;
            margin-top: 0;
        }
        .console-log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .test-steps {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="icon">🔍</span>分组表单数据回填调试测试</h1>
        
        <div class="debug-summary">
            <h3><span class="icon">🛠️</span>调试概述</h3>
            <p>已在编辑和克隆函数中添加详细的调试信息，包括数据查找、数据设置、弹窗显示等关键步骤的日志输出。同时添加了formData和弹窗状态的监听器来追踪数据变化。</p>
        </div>

        <div class="debug-section">
            <h2><span class="icon info-icon">📊</span>添加的调试信息</h2>
            
            <div class="debug-item success">
                <h3>1. 编辑函数调试信息</h3>
                <div class="code">
function handleEditGroup(id: string) {
  console.log('🔍 开始编辑分组，ID:', id);
  console.log('📋 当前所有分组数据:', groups.value);
  
  const group = groups.value.find(g => g.id === id);
  console.log('🎯 找到的分组数据:', group);
  
  if (group) {
    console.log('📝 开始设置表单数据...');
    
    // 设置编辑模式
    isEditMode.value = true;
    console.log('✅ 编辑模式已设置:', isEditMode.value);
    
    // 直接回填表单数据
    formData.id = group.id;
    formData.name = group.name;
    formData.description = group.description || '';
    formData.color = group.color || '#4ecdc4';
    formData.permissions = [...group.permissions];
    formData.status = group.status || 'active';
    
    console.log('📊 表单数据设置完成:', {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      color: formData.color,
      permissions: formData.permissions,
      status: formData.status
    });
    
    // 显示表单弹窗
    showGroupModal.value = true;
    console.log('🎭 弹窗已显示:', showGroupModal.value);
    
    // 在下一个tick清除表单验证状态
    nextTick(() => {
      console.log('⏰ nextTick执行，当前表单数据:', formData);
      if (formRef.value) {
        formRef.value.restoreValidation();
        console.log('🔄 表单验证状态已重置');
      }
    });
  } else {
    console.error('❌ 未找到指定ID的分组:', id);
  }
}
                </div>
            </div>

            <div class="debug-item success">
                <h3>2. 克隆函数调试信息</h3>
                <div class="code">
function handleCloneGroup(id: string) {
  console.log('🔍 开始克隆分组，ID:', id);
  console.log('📋 当前所有分组数据:', groups.value);
  
  const group = groups.value.find(g => g.id === id);
  console.log('🎯 找到的分组数据:', group);
  
  if (group) {
    console.log('📝 开始设置克隆表单数据...');
    
    // 设置为新增模式
    isEditMode.value = false;
    console.log('✅ 新增模式已设置:', isEditMode.value);
    
    // 直接回填表单数据（克隆原分组信息）
    formData.id = '';
    formData.name = `${group.name} - 副本`;
    formData.description = group.description || '';
    formData.color = group.color || '#4ecdc4';
    formData.permissions = [...group.permissions];
    formData.status = group.status || 'active';
    
    console.log('📊 克隆表单数据设置完成:', {
      id: formData.id,
      name: formData.name,
      description: formData.description,
      color: formData.color,
      permissions: formData.permissions,
      status: formData.status
    });
    
    // 显示表单弹窗
    showGroupModal.value = true;
    console.log('🎭 弹窗已显示:', showGroupModal.value);
    
    // 在下一个tick清除表单验证状态
    nextTick(() => {
      console.log('⏰ nextTick执行，当前克隆表单数据:', formData);
      if (formRef.value) {
        formRef.value.restoreValidation();
        console.log('🔄 表单验证状态已重置');
      }
    });
  } else {
    console.error('❌ 未找到指定ID的分组:', id);
  }
}
                </div>
            </div>

            <div class="debug-item success">
                <h3>3. 数据变化监听器</h3>
                <div class="code">
// 监听formData变化，用于调试
watch(
  () => formData,
  (newData) => {
    console.log('📊 formData发生变化:', {
      name: newData.name,
      description: newData.description,
      color: newData.color,
      permissions: newData.permissions,
      status: newData.status
    });
  },
  { deep: true }
);

// 监听弹窗显示状态
watch(
  () => showGroupModal.value,
  (newValue, oldValue) => {
    console.log('🎭 弹窗状态变化:', { from: oldValue, to: newValue });
    if (newValue) {
      console.log('📋 弹窗打开时的formData:', formData);
    }
  }
);
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h2><span class="icon warning-icon">🧪</span>调试测试步骤</h2>
            
            <div class="test-steps">
                <h3>测试编辑功能</h3>
                <ol>
                    <li>打开浏览器开发者工具，切换到Console标签</li>
                    <li>在分组管理页面，点击任意分组的"编辑"按钮</li>
                    <li>观察控制台输出的调试信息</li>
                    <li>检查是否能找到正确的分组数据</li>
                    <li>验证表单数据是否正确设置</li>
                    <li>确认弹窗打开时的formData内容</li>
                </ol>
            </div>

            <div class="test-steps">
                <h3>测试克隆功能</h3>
                <ol>
                    <li>在分组管理页面，点击分组操作菜单中的"克隆"选项</li>
                    <li>观察控制台输出的调试信息</li>
                    <li>检查是否能找到正确的分组数据</li>
                    <li>验证克隆表单数据是否正确设置</li>
                    <li>确认分组名称是否添加了"- 副本"后缀</li>
                    <li>验证其他字段是否正确复制</li>
                </ol>
            </div>
        </div>

        <div class="debug-section">
            <h2><span class="icon info-icon">📋</span>预期的控制台输出</h2>
            
            <div class="debug-item">
                <h3>编辑功能正常时的输出</h3>
                <div class="console-log">
🔍 开始编辑分组，ID: 1
📋 当前所有分组数据: [
  {
    id: '1',
    name: '管理员',
    description: '系统管理员，拥有所有权限',
    color: '#ff6b6b',
    permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
    userCount: 5,
    status: 'active',
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-15 14:30:00'
  },
  // ... 其他分组数据
]
🎯 找到的分组数据: {
  id: '1',
  name: '管理员',
  description: '系统管理员，拥有所有权限',
  color: '#ff6b6b',
  permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
  userCount: 5,
  status: 'active',
  createdAt: '2024-01-01 10:00:00',
  updatedAt: '2024-01-15 14:30:00'
}
📝 开始设置表单数据...
✅ 编辑模式已设置: true
📊 表单数据设置完成: {
  id: '1',
  name: '管理员',
  description: '系统管理员，拥有所有权限',
  color: '#ff6b6b',
  permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
  status: 'active'
}
🎭 弹窗已显示: true
🎭 弹窗状态变化: { from: false, to: true }
📋 弹窗打开时的formData: {
  id: '1',
  name: '管理员',
  description: '系统管理员，拥有所有权限',
  color: '#ff6b6b',
  permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
  status: 'active'
}
⏰ nextTick执行，当前表单数据: {
  id: '1',
  name: '管理员',
  description: '系统管理员，拥有所有权限',
  color: '#ff6b6b',
  permissions: ['browse', 'purchase', 'comment', 'merchantManage', 'productPublish'],
  status: 'active'
}
🔄 表单验证状态已重置
                </div>
            </div>

            <div class="debug-item error">
                <h3>可能的错误输出</h3>
                <div class="console-log">
❌ 未找到指定ID的分组: undefined
// 或者
🎯 找到的分组数据: undefined
// 或者
📊 formData发生变化: {
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [],
  status: 'active'
}
// 表示数据被重置了
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h2><span class="icon warning-icon">🔍</span>问题诊断指南</h2>
            
            <div class="debug-item warning">
                <h3>如果看到"未找到指定ID的分组"</h3>
                <ul>
                    <li>检查传入的ID参数是否正确</li>
                    <li>验证groups.value数组是否包含数据</li>
                    <li>确认分组数据的id字段类型（字符串vs数字）</li>
                </ul>
            </div>

            <div class="debug-item warning">
                <h3>如果找到了分组数据但表单仍显示默认值</h3>
                <ul>
                    <li>检查formData的响应式绑定是否正常</li>
                    <li>验证表单字段的v-model绑定</li>
                    <li>确认是否有其他地方重置了formData</li>
                    <li>检查弹窗组件的生命周期</li>
                </ul>
            </div>

            <div class="debug-item warning">
                <h3>如果数据设置正确但界面不更新</h3>
                <ul>
                    <li>检查Vue的响应式系统是否正常工作</li>
                    <li>验证组件的重新渲染机制</li>
                    <li>确认Naive UI组件的数据绑定</li>
                </ul>
            </div>
        </div>

        <div class="debug-section">
            <h2><span class="icon success-icon">🎯</span>下一步行动</h2>
            
            <div class="debug-summary">
                <p>📋 <strong>立即测试</strong>：按照上述步骤进行测试，观察控制台输出</p>
                <p>🔍 <strong>问题定位</strong>：根据控制台日志确定问题的具体位置</p>
                <p>🛠️ <strong>针对性修复</strong>：基于调试结果进行精确修复</p>
                <p>✅ <strong>验证修复</strong>：确认修复后功能正常工作</p>
                <p>🧹 <strong>清理调试代码</strong>：修复完成后移除调试信息</p>
            </div>
        </div>
    </div>
</body>
</html>
