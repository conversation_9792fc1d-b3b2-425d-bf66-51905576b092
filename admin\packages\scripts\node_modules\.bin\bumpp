#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules/bumpp/bin/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules/bumpp/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules/bumpp/bin/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules/bumpp/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/bumpp@10.2.0/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../bumpp/bin/bumpp.mjs" "$@"
else
  exec node  "$basedir/../bumpp/bin/bumpp.mjs" "$@"
fi
