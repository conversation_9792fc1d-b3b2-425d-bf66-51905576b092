<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'UserStatistics'
});

const appStore = useAppStore();

// 时间范围选择
const timeRange = ref<'7d' | '30d' | '90d' | '1y'>('30d');

// 统计数据
const statisticsData = computed(() => ({
  totalUsers: 15678,
  activeUsers: 12345,
  newUsers: 156,
  vipUsers: 2345,
  growthRate: 12.5,
  retentionRate: 78.3,
  avgSessionTime: '8m 32s',
  bounceRate: 23.7
}));

// 用户增长趋势图表
const { domRef: growthChartRef, updateOptions: updateGrowthChart } = useEcharts(() => ({
  title: {
    text: $t('page.userManagement.statistics.userGrowthTrend'),
    left: 'left',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    }
  },
  legend: {
    data: [$t('page.userManagement.statistics.newUsers'), $t('page.userManagement.statistics.totalUsers')],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07', '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14']
  },
  yAxis: [
    {
      type: 'value',
      name: $t('page.userManagement.statistics.newUsers'),
      position: 'left'
    },
    {
      type: 'value',
      name: $t('page.userManagement.statistics.totalUsers'),
      position: 'right'
    }
  ],
  series: [
    {
      name: $t('page.userManagement.statistics.newUsers'),
      type: 'bar',
      data: [120, 132, 101, 134, 90, 230, 210, 182, 191, 234, 290, 330, 310, 201],
      itemStyle: {
        color: '#3b82f6'
      }
    },
    {
      name: $t('page.userManagement.statistics.totalUsers'),
      type: 'line',
      yAxisIndex: 1,
      data: [15120, 15252, 15353, 15487, 15577, 15807, 16017, 16199, 16390, 16624, 16914, 17244, 17554, 17755],
      smooth: true,
      itemStyle: {
        color: '#10b981'
      }
    }
  ]
}));

// 用户来源分析饼图
const { domRef: sourceChartRef, updateOptions: updateSourceChart } = useEcharts(() => ({
  title: {
    text: $t('page.userManagement.statistics.userSourceAnalysis'),
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    top: 'middle'
  },
  series: [
    {
      name: $t('page.userManagement.statistics.userSource'),
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: [
        { value: 4235, name: $t('page.userManagement.statistics.sources.organic') },
        { value: 3102, name: $t('page.userManagement.statistics.sources.social') },
        { value: 2856, name: $t('page.userManagement.statistics.sources.direct') },
        { value: 2341, name: $t('page.userManagement.statistics.sources.referral') },
        { value: 1876, name: $t('page.userManagement.statistics.sources.email') },
        { value: 1268, name: $t('page.userManagement.statistics.sources.ads') }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}));

// 地域分布数据
const regionData = computed(() => [
  { region: $t('page.userManagement.statistics.regions.beijing'), users: 3245, percentage: 20.7 },
  { region: $t('page.userManagement.statistics.regions.shanghai'), users: 2856, percentage: 18.2 },
  { region: $t('page.userManagement.statistics.regions.guangzhou'), users: 2341, percentage: 14.9 },
  { region: $t('page.userManagement.statistics.regions.shenzhen'), users: 1987, percentage: 12.7 },
  { region: $t('page.userManagement.statistics.regions.hangzhou'), users: 1654, percentage: 10.5 },
  { region: $t('page.userManagement.statistics.regions.others'), users: 3595, percentage: 22.9 }
]);

// 初始化图表
onMounted(() => {
  setTimeout(() => {
    updateGrowthChart();
    updateSourceChart();
  }, 100);
});

// 监听时间范围变化，更新图表
watch(timeRange, () => {
  updateGrowthChart();
  updateSourceChart();
});

// 更新用户增长趋势图表的国际化文本
function updateGrowthChartLocale() {
  updateGrowthChart((opts, factory) => {
    // 重新生成完整的配置
    const newOpts = factory();

    // 保持数据不变，只更新文本
    newOpts.series[0].data = opts.series[0].data;
    newOpts.series[1].data = opts.series[1].data;
    newOpts.xAxis.data = opts.xAxis.data;

    return newOpts;
  });
}

// 更新用户来源分析图表的国际化文本
function updateSourceChartLocale() {
  updateSourceChart((opts, factory) => {
    // 重新生成完整的配置
    const newOpts = factory();

    // 保持数据值不变，只更新文本标签
    newOpts.series[0].data = newOpts.series[0].data.map((item, index) => ({
      ...item,
      value: opts.series[0].data[index]?.value || item.value
    }));

    return newOpts;
  });
}

// 监听语言变化，更新图表
watch(
  () => appStore.locale,
  () => {
    updateGrowthChartLocale();
    updateSourceChartLocale();
  }
);
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ $t('page.userManagement.statistics.title') }}
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            {{ $t('page.userManagement.statistics.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NSelect
            v-model="timeRange"
            :options="[
              { label: $t('page.userManagement.statistics.timeRange.7d'), value: '7d' },
              { label: $t('page.userManagement.statistics.timeRange.30d'), value: '30d' },
              { label: $t('page.userManagement.statistics.timeRange.90d'), value: '90d' },
              { label: $t('page.userManagement.statistics.timeRange.1y'), value: '1y' }
            ]"
            style="width: 120px"
          />
          <NButton type="primary">
            <template #icon>
              <SvgIcon icon="mdi:refresh" />
            </template>
            {{ $t('common.refresh') }}
          </NButton>
        </div>
      </div>

      <!-- 关键指标卡片 -->
      <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <NGi span="24 s:12 m:6">
          <NCard :bordered="false" class="card-wrapper">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-12px text-gray-500 mb-4px">{{ $t('page.userManagement.statistics.totalUsers') }}</div>
                <div class="text-24px font-bold text-gray-900 dark:text-gray-100">{{ statisticsData.totalUsers.toLocaleString() }}</div>
                <div class="text-12px text-green-500 mt-4px">+{{ statisticsData.growthRate }}%</div>
              </div>
              <div class="w-48px h-48px bg-blue-100 dark:bg-blue-900/30 rd-12px flex items-center justify-center">
                <SvgIcon icon="mdi:account-group" class="text-24px text-blue-500" />
              </div>
            </div>
          </NCard>
        </NGi>
        <NGi span="24 s:12 m:6">
          <NCard :bordered="false" class="card-wrapper">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-12px text-gray-500 mb-4px">{{ $t('page.userManagement.statistics.activeUsers') }}</div>
                <div class="text-24px font-bold text-gray-900 dark:text-gray-100">{{ statisticsData.activeUsers.toLocaleString() }}</div>
                <div class="text-12px text-green-500 mt-4px">{{ statisticsData.retentionRate }}%</div>
              </div>
              <div class="w-48px h-48px bg-green-100 dark:bg-green-900/30 rd-12px flex items-center justify-center">
                <SvgIcon icon="mdi:account-check" class="text-24px text-green-500" />
              </div>
            </div>
          </NCard>
        </NGi>
        <NGi span="24 s:12 m:6">
          <NCard :bordered="false" class="card-wrapper">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-12px text-gray-500 mb-4px">{{ $t('page.userManagement.statistics.newUsers') }}</div>
                <div class="text-24px font-bold text-gray-900 dark:text-gray-100">{{ statisticsData.newUsers }}</div>
                <div class="text-12px text-orange-500 mt-4px">{{ $t('page.userManagement.statistics.today') }}</div>
              </div>
              <div class="w-48px h-48px bg-orange-100 dark:bg-orange-900/30 rd-12px flex items-center justify-center">
                <SvgIcon icon="mdi:account-plus" class="text-24px text-orange-500" />
              </div>
            </div>
          </NCard>
        </NGi>
        <NGi span="24 s:12 m:6">
          <NCard :bordered="false" class="card-wrapper">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-12px text-gray-500 mb-4px">{{ $t('page.userManagement.statistics.vipUsers') }}</div>
                <div class="text-24px font-bold text-gray-900 dark:text-gray-100">{{ statisticsData.vipUsers.toLocaleString() }}</div>
                <div class="text-12px text-yellow-500 mt-4px">{{ statisticsData.bounceRate }}%</div>
              </div>
              <div class="w-48px h-48px bg-yellow-100 dark:bg-yellow-900/30 rd-12px flex items-center justify-center">
                <SvgIcon icon="mdi:crown" class="text-24px text-yellow-500" />
              </div>
            </div>
          </NCard>
        </NGi>
      </NGrid>

      <!-- 图表区域 -->
      <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <!-- 用户增长趋势 -->
        <NGi span="24 m:16">
          <NCard :bordered="false" class="card-wrapper">
            <div ref="growthChartRef" class="h-400px"></div>
          </NCard>
        </NGi>
        
        <!-- 用户来源分析 -->
        <NGi span="24 m:8">
          <NCard :bordered="false" class="card-wrapper">
            <div ref="sourceChartRef" class="h-400px"></div>
          </NCard>
        </NGi>
      </NGrid>

      <!-- 地域分布 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center">
            <SvgIcon icon="mdi:map" class="text-20px mr-8px text-primary" />
            <span class="text-16px font-medium">{{ $t('page.userManagement.statistics.regionDistribution') }}</span>
          </div>
        </template>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-16px">
          <div v-for="item in regionData" :key="item.region" class="flex items-center justify-between p-12px bg-gray-50 dark:bg-gray-800 rd-8px">
            <div class="flex items-center space-x-8px">
              <div class="w-8px h-8px bg-primary rd-full"></div>
              <span class="text-14px text-gray-900 dark:text-gray-100">{{ item.region }}</span>
            </div>
            <div class="text-right">
              <div class="text-14px font-medium text-gray-900 dark:text-gray-100">{{ item.users.toLocaleString() }}</div>
              <div class="text-12px text-gray-500">{{ item.percentage }}%</div>
            </div>
          </div>
        </div>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped></style>
