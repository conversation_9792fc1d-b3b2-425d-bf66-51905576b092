<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h, watch, reactive } from 'vue';
import type { DataTableColumns, DataTableRowKey, FormInst, FormRules } from 'naive-ui';
import {
  NButton,
  NTag,
  NPopconfirm,
  useMessage,
  useDialog,
  NSpace,
  NCard,
  NGrid,
  NGi,
  NInput,
  NSelect,
  NDataTable,
  NPagination,
  NModal,
  NForm,
  NFormItem,
  NColorPicker,
  NBadge,
  NCollapse,
  NCollapseItem,
  NEmpty,
  NTooltip,
  NDropdown,
  NDrawer,
  NDrawerContent,
  NTabs,
  NTabPane,
  NList,
  NListItem,
  NThing
} from 'naive-ui';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'UserTagManagement'
});

const appStore = useAppStore();
const message = useMessage();
const dialog = useDialog();

// 标签分类接口
interface TagCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  tagCount: number;
  isExpanded: boolean;
  order: number;
}

// 标签接口
interface TagInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  categoryId: string;
  userCount: number;
  status: 'active' | 'disabled';
  createdAt: string;
  updatedAt: string;
}

// 模拟分类数据
const categories = ref<TagCategory[]>([
  {
    id: '1',
    name: '用户状态',
    description: '用户当前状态相关标签',
    icon: 'mdi:account-circle',
    color: '#3b82f6',
    tagCount: 4,
    isExpanded: true,
    order: 1
  },
  {
    id: '2',
    name: '消费行为',
    description: '用户消费行为相关标签',
    icon: 'mdi:shopping',
    color: '#10b981',
    tagCount: 3,
    isExpanded: false,
    order: 2
  },
  {
    id: '3',
    name: '地域分布',
    description: '用户地理位置相关标签',
    icon: 'mdi:map-marker',
    color: '#f59e0b',
    tagCount: 2,
    isExpanded: false,
    order: 3
  },
  {
    id: '4',
    name: '设备类型',
    description: '用户设备类型相关标签',
    icon: 'mdi:devices',
    color: '#8b5cf6',
    tagCount: 2,
    isExpanded: false,
    order: 4
  },
  {
    id: '5',
    name: '用户类型',
    description: '用户身份类型相关标签',
    icon: 'mdi:account-group',
    color: '#06b6d4',
    tagCount: 1,
    isExpanded: false,
    order: 5
  }
]);

// 模拟标签数据
const tags = ref<TagInfo[]>([
  {
    id: '1',
    name: '新用户',
    description: '注册时间在30天内的用户',
    color: '#3b82f6',
    categoryId: '1',
    userCount: 156,
    status: 'active',
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-15 14:30:00'
  },
  {
    id: '2',
    name: '活跃用户',
    description: '最近7天内有登录行为的用户',
    color: '#10b981',
    categoryId: '1',
    userCount: 8456,
    status: 'active',
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-12-15 10:30:00'
  },
  {
    id: '3',
    name: '流失用户',
    description: '超过30天未登录的用户',
    color: '#ef4444',
    categoryId: '1',
    userCount: 1234,
    status: 'active',
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-12-10 09:15:00'
  },
  {
    id: '4',
    name: '问题用户',
    description: '存在违规行为的用户',
    color: '#dc2626',
    categoryId: '1',
    userCount: 23,
    status: 'disabled',
    createdAt: '2024-02-01 00:00:00',
    updatedAt: '2024-12-22 08:30:00'
  },
  {
    id: '5',
    name: '高价值客户',
    description: '累计消费金额超过5000元的用户',
    color: '#f59e0b',
    categoryId: '2',
    userCount: 234,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-20 14:20:00'
  },
  {
    id: '6',
    name: '低消费用户',
    description: '累计消费金额低于100元的用户',
    color: '#6b7280',
    categoryId: '2',
    userCount: 3456,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-18 16:45:00'
  },
  {
    id: '7',
    name: '频繁购买用户',
    description: '最近30天内购买次数超过5次的用户',
    color: '#059669',
    categoryId: '2',
    userCount: 567,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-19 11:20:00'
  },
  {
    id: '8',
    name: '北京用户',
    description: '注册地址在北京的用户',
    color: '#8b5cf6',
    categoryId: '3',
    userCount: 3245,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-11-05 16:45:00'
  },
  {
    id: '9',
    name: '上海用户',
    description: '注册地址在上海的用户',
    color: '#7c3aed',
    categoryId: '3',
    userCount: 2876,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-11-08 13:25:00'
  },
  {
    id: '10',
    name: '移动端用户',
    description: '主要使用移动设备访问的用户',
    color: '#06b6d4',
    categoryId: '4',
    userCount: 9876,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-01 11:20:00'
  },
  {
    id: '11',
    name: 'PC端用户',
    description: '主要使用PC设备访问的用户',
    color: '#0891b2',
    categoryId: '4',
    userCount: 4321,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-03 09:15:00'
  },
  {
    id: '12',
    name: '企业用户',
    description: '企业认证用户',
    color: '#059669',
    categoryId: '5',
    userCount: 567,
    status: 'active',
    createdAt: '2024-01-01 00:00:00',
    updatedAt: '2024-12-18 13:15:00'
  }
]);

// 当前选中的分类
const selectedCategoryId = ref<string>('');
const selectedCategoryName = ref<string>('全部标签');

// 搜索和筛选状态
const searchQuery = ref('');
const selectedStatus = ref<string | null>(null);
const showAdvancedFilter = ref(false);

// 分页
const pagination = ref({
  page: 1,
  pageSize: 12,
  total: 0
});

// 选中的行
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 表单相关状态
const showTagModal = ref(false);
const showCategoryModal = ref(false);
const isEditMode = ref(false);
const formRef = ref<FormInst | null>(null);
const categoryFormRef = ref<FormInst | null>(null);

// 标签详情抽屉状态
const showTagDetail = ref(false);
const currentTagId = ref('');

// 响应式抽屉宽度
const drawerWidth = computed(() => {
  if (typeof window === 'undefined') return 1000;
  const screenWidth = window.innerWidth;
  if (screenWidth >= 1400) return 1200;
  if (screenWidth >= 1200) return 1000;
  if (screenWidth >= 768) return '80%';
  return '95%';
});

// 当前标签详情
const currentTagDetail = computed(() => {
  return tags.value.find(tag => tag.id === currentTagId.value);
});

// 标签表单数据
const tagFormData = reactive({
  id: '',
  name: '',
  description: '',
  color: '#3b82f6',
  categoryId: '',
  status: 'active' as 'active' | 'disabled'
});

// 分类表单数据
const categoryFormData = reactive({
  id: '',
  name: '',
  description: '',
  icon: 'mdi:tag',
  color: '#3b82f6',
  order: 1
});

// 表单验证规则
const tagFormRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: $t('page.userManagement.tags.nameRequired'), trigger: 'blur' },
    { min: 2, max: 20, message: '标签名称长度应在2-20个字符之间', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: $t('page.userManagement.tags.categoryRequired'), trigger: 'change' }
  ],
  description: [
    { max: 100, message: '描述长度不能超过100个字符', trigger: 'blur' }
  ]
}));

const categoryFormRules = computed<FormRules>(() => ({
  name: [
    { required: true, message: '分类名称不能为空', trigger: 'blur' },
    { min: 2, max: 20, message: '分类名称长度应在2-20个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 100, message: '描述长度不能超过100个字符', trigger: 'blur' }
  ]
}));

// 状态选项
const statusOptions = computed(() => [
  { label: $t('page.userManagement.tags.status.active'), value: 'active' },
  { label: $t('page.userManagement.tags.status.disabled'), value: 'disabled' }
]);

// 分类选项
const categoryOptions = computed(() =>
  categories.value.map(cat => ({
    label: cat.name,
    value: cat.id
  }))
);

// 筛选后的标签数据
const filteredTags = computed(() => {
  let result = tags.value;

  // 分类筛选
  if (selectedCategoryId.value) {
    result = result.filter(tag => tag.categoryId === selectedCategoryId.value);
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(tag =>
      tag.name.toLowerCase().includes(query) ||
      tag.description.toLowerCase().includes(query)
    );
  }

  // 状态筛选
  if (selectedStatus.value) {
    result = result.filter(tag => tag.status === selectedStatus.value);
  }

  return result;
});

// 分页数据
const paginatedTags = computed(() => {
  const start = (pagination.value.page - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredTags.value.slice(start, end);
});

// 更新分页信息
function updatePagination() {
  pagination.value.total = filteredTags.value.length;
  const maxPage = Math.ceil(pagination.value.total / pagination.value.pageSize);
  if (pagination.value.page > maxPage && maxPage > 0) {
    pagination.value.page = maxPage;
  }
}

// 分类操作函数
function handleSelectCategory(categoryId: string, categoryName: string) {
  selectedCategoryId.value = categoryId;
  selectedCategoryName.value = categoryName;
  pagination.value.page = 1;
  updatePagination();
}

function handleToggleCategoryExpand(categoryId: string) {
  const category = categories.value.find(cat => cat.id === categoryId);
  if (category) {
    category.isExpanded = !category.isExpanded;
  }
}

function handleAddCategory() {
  isEditMode.value = false;
  resetCategoryForm();
  showCategoryModal.value = true;
}

function handleEditCategory(categoryId: string) {
  const category = categories.value.find(cat => cat.id === categoryId);
  if (category) {
    isEditMode.value = true;
    categoryFormData.id = category.id;
    categoryFormData.name = category.name;
    categoryFormData.description = category.description;
    categoryFormData.icon = category.icon;
    categoryFormData.color = category.color;
    categoryFormData.order = category.order;
    showCategoryModal.value = true;
  }
}

function handleDeleteCategory(categoryId: string) {
  const category = categories.value.find(cat => cat.id === categoryId);
  if (!category) return;

  // 检查是否有标签使用此分类
  const hasTagsInCategory = tags.value.some(tag => tag.categoryId === categoryId);
  if (hasTagsInCategory) {
    message.warning('该分类下还有标签，无法删除');
    return;
  }

  dialog.warning({
    title: '删除分类',
    content: `确认删除分类"${category.name}"？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = categories.value.findIndex(cat => cat.id === categoryId);
      if (index > -1) {
        categories.value.splice(index, 1);
        message.success('删除分类成功');
        // 如果当前选中的分类被删除，切换到全部标签
        if (selectedCategoryId.value === categoryId) {
          handleSelectCategory('', '全部标签');
        }
      }
    }
  });
}

// 标签操作函数
function handleSearch() {
  pagination.value.page = 1;
  updatePagination();
}

function handleReset() {
  searchQuery.value = '';
  selectedStatus.value = null;
  pagination.value.page = 1;
  updatePagination();
}

function handleAddTag() {
  isEditMode.value = false;
  resetTagForm();
  // 如果当前选中了分类，默认设置为该分类
  if (selectedCategoryId.value) {
    tagFormData.categoryId = selectedCategoryId.value;
  }
  showTagModal.value = true;
}

function handleEditTag(tagId: string) {
  const tag = tags.value.find(t => t.id === tagId);
  if (tag) {
    isEditMode.value = true;
    tagFormData.id = tag.id;
    tagFormData.name = tag.name;
    tagFormData.description = tag.description;
    tagFormData.color = tag.color;
    tagFormData.categoryId = tag.categoryId;
    tagFormData.status = tag.status;
    showTagModal.value = true;
  }
}

function handleViewTag(tagId: string) {
  currentTagId.value = tagId;
  showTagDetail.value = true;
}

function handleDeleteTag(tagId: string) {
  const tag = tags.value.find(t => t.id === tagId);
  if (!tag) return;

  dialog.warning({
    title: '删除标签',
    content: `确认删除标签"${tag.name}"？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = tags.value.findIndex(t => t.id === tagId);
      if (index > -1) {
        tags.value.splice(index, 1);
        message.success('删除标签成功');
        updatePagination();
        updateCategoryTagCount();
      }
    }
  });
}

// 表单重置函数
function resetTagForm() {
  tagFormData.id = '';
  tagFormData.name = '';
  tagFormData.description = '';
  tagFormData.color = '#3b82f6';
  tagFormData.categoryId = '';
  tagFormData.status = 'active';
}

function resetCategoryForm() {
  categoryFormData.id = '';
  categoryFormData.name = '';
  categoryFormData.description = '';
  categoryFormData.icon = 'mdi:tag';
  categoryFormData.color = '#3b82f6';
  categoryFormData.order = categories.value.length + 1;
}

// 更新分类标签数量
function updateCategoryTagCount() {
  categories.value.forEach(category => {
    category.tagCount = tags.value.filter(tag => tag.categoryId === category.id).length;
  });
}

// 表单提交函数
function handleSubmitTag() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (isEditMode.value) {
        // 编辑标签
        const tag = tags.value.find(t => t.id === tagFormData.id);
        if (tag) {
          tag.name = tagFormData.name;
          tag.description = tagFormData.description;
          tag.color = tagFormData.color;
          tag.categoryId = tagFormData.categoryId;
          tag.status = tagFormData.status;
          tag.updatedAt = new Date().toLocaleString();
          message.success('更新标签成功');
        }
      } else {
        // 新增标签
        const newTag: TagInfo = {
          id: Date.now().toString(),
          name: tagFormData.name,
          description: tagFormData.description,
          color: tagFormData.color,
          categoryId: tagFormData.categoryId,
          userCount: 0,
          status: tagFormData.status,
          createdAt: new Date().toLocaleString(),
          updatedAt: new Date().toLocaleString()
        };
        tags.value.unshift(newTag);
        message.success('创建标签成功');
      }
      showTagModal.value = false;
      updatePagination();
      updateCategoryTagCount();
    }
  });
}

function handleCancelTag() {
  showTagModal.value = false;
  resetTagForm();
}

function handleSubmitCategory() {
  categoryFormRef.value?.validate((errors) => {
    if (!errors) {
      if (isEditMode.value) {
        // 编辑分类
        const category = categories.value.find(cat => cat.id === categoryFormData.id);
        if (category) {
          category.name = categoryFormData.name;
          category.description = categoryFormData.description;
          category.icon = categoryFormData.icon;
          category.color = categoryFormData.color;
          category.order = categoryFormData.order;
          message.success('更新分类成功');
        }
      } else {
        // 新增分类
        const newCategory: TagCategory = {
          id: Date.now().toString(),
          name: categoryFormData.name,
          description: categoryFormData.description,
          icon: categoryFormData.icon,
          color: categoryFormData.color,
          tagCount: 0,
          isExpanded: false,
          order: categoryFormData.order
        };
        categories.value.push(newCategory);
        // 按order排序
        categories.value.sort((a, b) => a.order - b.order);
        message.success('创建分类成功');
      }
      showCategoryModal.value = false;
    }
  });
}

function handleCancelCategory() {
  showCategoryModal.value = false;
  resetCategoryForm();
}

// 监听筛选条件变化
watch([searchQuery, selectedStatus, selectedCategoryId], () => {
  pagination.value.page = 1;
  updatePagination();
});

// 监听语言变化
watch(
  () => appStore.locale,
  () => {
    // 强制更新组件以重新计算国际化文本
  }
);

// 初始化
updatePagination();
updateCategoryTagCount();
</script>

<template>
  <div class="min-h-full overflow-hidden">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between p-16px bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div>
        <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
          {{ $t('page.userManagement.tags.title') }}
        </h1>
        <p class="text-14px text-gray-600 dark:text-gray-400">
          {{ $t('page.userManagement.tags.description') }}
        </p>
      </div>
      <div class="flex items-center space-x-12px">
        <NButton type="primary" @click="handleAddTag">
          <template #icon>
            <SvgIcon icon="mdi:plus" />
          </template>
          {{ $t('page.userManagement.tags.createTag') }}
        </NButton>
        <NButton @click="handleAddCategory">
          <template #icon>
            <SvgIcon icon="mdi:folder-plus" />
          </template>
          {{ $t('page.userManagement.tags.createCategory') }}
        </NButton>
      </div>
    </div>

    <!-- 主要内容区域 - 左右分栏布局 -->
    <div class="flex h-[calc(100vh-120px)]">
      <!-- 左侧分类导航栏 -->
      <div class="w-280px bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
        <div class="p-16px">
          <!-- 全部标签选项 -->
          <div
            class="flex items-center justify-between p-12px rounded-8px cursor-pointer transition-colors"
            :class="selectedCategoryId === '' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'hover:bg-gray-50 dark:hover:bg-gray-800'"
            @click="handleSelectCategory('', '全部标签')"
          >
            <div class="flex items-center space-x-8px">
              <SvgIcon icon="mdi:tag-multiple" class="text-16px" />
              <span class="font-medium">全部标签</span>
            </div>
            <NBadge :value="tags.length" :max="999" />
          </div>

          <!-- 分类列表 -->
          <div class="mt-16px">
            <div class="flex items-center justify-between mb-12px">
              <span class="text-12px font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                {{ $t('page.userManagement.tags.categories') }}
              </span>
              <NTooltip>
                <template #trigger>
                  <NButton size="tiny" text @click="handleAddCategory">
                    <SvgIcon icon="mdi:plus" class="text-12px" />
                  </NButton>
                </template>
                {{ $t('page.userManagement.tags.createCategory') }}
              </NTooltip>
            </div>

            <div class="space-y-4px">
              <div
                v-for="category in categories"
                :key="category.id"
                class="group"
              >
                <!-- 分类项 -->
                <div
                  class="flex items-center justify-between p-12px rounded-8px cursor-pointer transition-colors"
                  :class="selectedCategoryId === category.id ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'hover:bg-gray-50 dark:hover:bg-gray-800'"
                  @click="handleSelectCategory(category.id, category.name)"
                >
                  <div class="flex items-center space-x-8px flex-1 min-w-0">
                    <div
                      class="w-12px h-12px rounded-full flex-shrink-0"
                      :style="{ backgroundColor: category.color }"
                    />
                    <SvgIcon :icon="category.icon" class="text-16px flex-shrink-0" />
                    <span class="font-medium truncate">{{ category.name }}</span>
                  </div>
                  <div class="flex items-center space-x-4px">
                    <NBadge :value="category.tagCount" :max="999" />
                    <NDropdown
                      :options="[
                        { label: '编辑分类', key: 'edit', icon: () => h(SvgIcon, { icon: 'mdi:pencil' }) },
                        { label: '删除分类', key: 'delete', icon: () => h(SvgIcon, { icon: 'mdi:delete' }), disabled: category.tagCount > 0 }
                      ]"
                      @select="(key) => {
                        if (key === 'edit') handleEditCategory(category.id);
                        else if (key === 'delete') handleDeleteCategory(category.id);
                      }"
                    >
                      <NButton
                        size="tiny"
                        text
                        class="opacity-0 group-hover:opacity-100 transition-opacity"
                        @click.stop
                      >
                        <SvgIcon icon="mdi:dots-vertical" class="text-12px" />
                      </NButton>
                    </NDropdown>
                  </div>
                </div>

                <!-- 分类描述 -->
                <div
                  v-if="selectedCategoryId === category.id && category.description"
                  class="ml-36px mr-12px mb-8px"
                >
                  <p class="text-12px text-gray-500 dark:text-gray-400">
                    {{ category.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧标签列表展示区 -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 搜索和筛选区域 -->
        <div class="p-16px bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-16px">
            <h2 class="text-18px font-semibold text-gray-900 dark:text-gray-100">
              {{ selectedCategoryName }}
            </h2>
            <div class="text-14px text-gray-500">
              {{ $t('page.userManagement.tags.totalCount', { total: filteredTags.length }) }}
            </div>
          </div>

          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:12 l:8">
              <NInput
                v-model:value="searchQuery"
                :placeholder="$t('page.userManagement.tags.searchPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:6 l:4">
              <NSelect
                v-model:value="selectedStatus"
                :placeholder="$t('page.userManagement.tags.selectStatus')"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:6 l:4">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  {{ $t('page.userManagement.tags.search') }}
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ $t('page.userManagement.tags.reset') }}
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>
        </div>

        <!-- 标签展示区域 -->
        <div class="flex-1 overflow-y-auto p-16px bg-gray-50 dark:bg-gray-800">
          <div v-if="paginatedTags.length === 0" class="flex items-center justify-center h-full">
            <NEmpty :description="$t('page.userManagement.tags.noData')" />
          </div>
          <div v-else>
            <!-- 标签卡片网格 -->
            <NGrid :x-gap="16" :y-gap="16" :cols="4" responsive="screen">
              <NGi
                v-for="tag in paginatedTags"
                :key="tag.id"
                span="4 s:2 m:1"
              >
                <NCard
                  class="tag-card cursor-pointer transition-all duration-200 hover:shadow-lg"
                  :class="tag.status === 'disabled' ? 'opacity-60' : ''"
                  @click="handleViewTag(tag.id)"
                >
                  <div class="space-y-12px">
                    <!-- 标签头部 -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-8px">
                        <div
                          class="w-16px h-16px rounded-full"
                          :style="{ backgroundColor: tag.color }"
                        />
                        <span class="font-medium text-16px truncate">{{ tag.name }}</span>
                      </div>
                      <NDropdown
                        :options="[
                          { label: '查看详情', key: 'view', icon: () => h(SvgIcon, { icon: 'mdi:eye' }) },
                          { label: '编辑标签', key: 'edit', icon: () => h(SvgIcon, { icon: 'mdi:pencil' }) },
                          { type: 'divider' },
                          { label: '删除标签', key: 'delete', icon: () => h(SvgIcon, { icon: 'mdi:delete' }) }
                        ]"
                        @select="(key) => {
                          if (key === 'view') handleViewTag(tag.id);
                          else if (key === 'edit') handleEditTag(tag.id);
                          else if (key === 'delete') handleDeleteTag(tag.id);
                        }"
                      >
                        <NButton size="small" text @click.stop>
                          <SvgIcon icon="mdi:dots-vertical" />
                        </NButton>
                      </NDropdown>
                    </div>

                    <!-- 标签描述 -->
                    <p class="text-14px text-gray-600 dark:text-gray-400 line-clamp-2">
                      {{ tag.description || '暂无描述' }}
                    </p>

                    <!-- 标签统计 -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-4px">
                        <SvgIcon icon="mdi:account-group" class="text-14px text-gray-500" />
                        <span class="text-14px text-gray-600 dark:text-gray-400">
                          {{ tag.userCount }} 用户
                        </span>
                      </div>
                      <NTag
                        :type="tag.status === 'active' ? 'success' : 'error'"
                        size="small"
                      >
                        {{ tag.status === 'active' ? '启用' : '禁用' }}
                      </NTag>
                    </div>

                    <!-- 更新时间 -->
                    <div class="text-12px text-gray-500 dark:text-gray-400">
                      更新于 {{ new Date(tag.updatedAt).toLocaleDateString() }}
                    </div>
                  </div>
                </NCard>
              </NGi>
            </NGrid>

            <!-- 分页 -->
            <div class="flex items-center justify-between mt-24px">
              <div class="text-14px text-gray-600">
                {{ $t('page.userManagement.tags.showingRecords', {
                  start: (pagination.page - 1) * pagination.pageSize + 1,
                  end: Math.min(pagination.page * pagination.pageSize, pagination.total),
                  total: pagination.total
                }) }}
              </div>
              <NPagination
                v-model:page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :total="pagination.total"
                :page-sizes="[12, 24, 48, 96]"
                show-size-picker
                show-quick-jumper
                @update:page="updatePagination"
                @update:page-size="updatePagination"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签表单弹窗 -->
    <NModal
      v-model:show="showTagModal"
      preset="dialog"
      :title="isEditMode ? $t('page.userManagement.tags.editTag') : $t('page.userManagement.tags.createTag')"
      class="w-600px"
    >
      <NForm
        ref="formRef"
        :model="tagFormData"
        :rules="tagFormRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <NGrid :x-gap="24" :y-gap="18" :cols="2">
          <NGi>
            <NFormItem :label="$t('page.userManagement.tags.form.name')" path="name">
              <NInput
                v-model:value="tagFormData.name"
                :placeholder="$t('page.userManagement.tags.form.namePlaceholder')"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.tags.form.color')" path="color">
              <NColorPicker v-model:value="tagFormData.color" :modes="['hex']" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.tags.form.category')" path="categoryId">
              <NSelect
                v-model:value="tagFormData.categoryId"
                :placeholder="$t('page.userManagement.tags.form.categoryPlaceholder')"
                :options="categoryOptions"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem :label="$t('page.userManagement.tags.form.status')" path="status">
              <NSelect
                v-model:value="tagFormData.status"
                :options="statusOptions"
              />
            </NFormItem>
          </NGi>
          <NGi :span="2">
            <NFormItem :label="$t('page.userManagement.tags.form.description')" path="description">
              <NInput
                v-model:value="tagFormData.description"
                type="textarea"
                :placeholder="$t('page.userManagement.tags.form.descriptionPlaceholder')"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </NFormItem>
          </NGi>
        </NGrid>

        <template #action>
          <NSpace>
            <NButton @click="handleCancelTag">
              {{ $t('page.userManagement.tags.form.cancel') }}
            </NButton>
            <NButton type="primary" @click="handleSubmitTag">
              {{ $t('page.userManagement.tags.form.save') }}
            </NButton>
          </NSpace>
        </template>
      </NForm>
    </NModal>

    <!-- 分类表单弹窗 -->
    <NModal
      v-model:show="showCategoryModal"
      preset="dialog"
      :title="isEditMode ? '编辑分类' : '创建分类'"
      class="w-500px"
    >
      <NForm
        ref="categoryFormRef"
        :model="categoryFormData"
        :rules="categoryFormRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <NGrid :x-gap="24" :y-gap="18" :cols="2">
          <NGi>
            <NFormItem label="分类名称" path="name">
              <NInput
                v-model:value="categoryFormData.name"
                placeholder="请输入分类名称"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="分类颜色" path="color">
              <NColorPicker v-model:value="categoryFormData.color" :modes="['hex']" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="图标" path="icon">
              <NInput
                v-model:value="categoryFormData.icon"
                placeholder="请输入图标名称"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="排序" path="order">
              <NInput
                v-model:value="categoryFormData.order"
                type="number"
                placeholder="排序值"
              />
            </NFormItem>
          </NGi>
          <NGi :span="2">
            <NFormItem label="分类描述" path="description">
              <NInput
                v-model:value="categoryFormData.description"
                type="textarea"
                placeholder="请输入分类描述"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </NFormItem>
          </NGi>
        </NGrid>

        <template #action>
          <NSpace>
            <NButton @click="handleCancelCategory">
              取消
            </NButton>
            <NButton type="primary" @click="handleSubmitCategory">
              保存
            </NButton>
          </NSpace>
        </template>
      </NForm>
    </NModal>

    <!-- 标签详情抽屉 -->
    <NDrawer v-model:show="showTagDetail" :width="drawerWidth" placement="right">
      <NDrawerContent :title="$t('page.userManagement.tags.drawer.tagDetail')" closable>
        <div v-if="currentTagDetail">
          <NTabs type="line" animated>
            <!-- 标签信息选项卡 -->
            <NTabPane name="tagInfo" :tab="$t('page.userManagement.tags.drawer.tagInfo')">
              <div class="space-y-24px">
                <!-- 标签概览 -->
                <NCard title="标签概览" size="small">
                  <div class="flex items-center mb-16px">
                    <div
                      class="w-40px h-40px rounded-full mr-16px"
                      :style="{ backgroundColor: currentTagDetail.color }"
                    />
                    <div>
                      <h3 class="text-18px font-medium mb-4px">{{ currentTagDetail.name }}</h3>
                      <p class="text-14px text-gray-500">{{ currentTagDetail.description }}</p>
                    </div>
                  </div>

                  <NGrid :x-gap="16" :y-gap="16" :cols="2">
                    <NGi>
                      <div class="text-center p-16px bg-gray-50 dark:bg-gray-800 rounded-8px">
                        <div class="text-24px font-bold text-blue-600">{{ currentTagDetail.userCount }}</div>
                        <div class="text-14px text-gray-500 mt-4px">用户数量</div>
                      </div>
                    </NGi>
                    <NGi>
                      <div class="text-center p-16px bg-gray-50 dark:bg-gray-800 rounded-8px">
                        <div class="text-24px font-bold text-green-600">
                          {{ currentTagDetail.status === 'active' ? '启用' : '禁用' }}
                        </div>
                        <div class="text-14px text-gray-500 mt-4px">状态</div>
                      </div>
                    </NGi>
                  </NGrid>
                </NCard>

                <!-- 标签统计 -->
                <NCard title="使用统计" size="small">
                  <div class="text-center py-32px text-gray-500">
                    暂无统计数据
                  </div>
                </NCard>
              </div>
            </NTabPane>
          </NTabs>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped>
/* 标签卡片样式 */
.tag-card {
  transition: all 0.2s ease-in-out;
}

.tag-card:hover {
  transform: translateY(-2px);
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .w-280px {
    width: 240px;
  }
}

@media (max-width: 640px) {
  .w-280px {
    width: 200px;
  }
}
</style>