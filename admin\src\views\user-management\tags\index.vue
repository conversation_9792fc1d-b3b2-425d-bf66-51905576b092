<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h } from 'vue';
import type { DataTableColumns, DataTableRowKey } from 'naive-ui';
import { NButton, NTag, NPopconfirm, useMessage } from 'naive-ui';
// import { $t } from '@/locales';

defineOptions({
  name: 'UserTags'
});

const message = useMessage();

interface TagInfo {
  id: string;
  name: string;
  description: string;
  color: string;
  category: string;
  userCount: number;
  status: 'active' | 'disabled';
  createTime: string;
  updateTime: string;
}

// 模拟标签数据
const tags = ref<TagInfo[]>([
  {
    id: '1',
    name: '新用户',
    description: '注册时间在30天内的用户',
    color: '#3b82f6',
    category: '用户状态',
    userCount: 156,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '2',
    name: '活跃用户',
    description: '最近7天内有登录行为的用户',
    color: '#10b981',
    category: '用户状态',
    userCount: 8456,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-15 10:30:00'
  },
  {
    id: '3',
    name: '高价值客户',
    description: '累计消费金额超过5000元的用户',
    color: '#f59e0b',
    category: '消费行为',
    userCount: 234,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-20 14:20:00'
  },
  {
    id: '4',
    name: '流失用户',
    description: '超过30天未登录的用户',
    color: '#ef4444',
    category: '用户状态',
    userCount: 1234,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-10 09:15:00'
  },
  {
    id: '5',
    name: '北京用户',
    description: '注册地址在北京的用户',
    color: '#8b5cf6',
    category: '地域分布',
    userCount: 3245,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-11-05 16:45:00'
  },
  {
    id: '6',
    name: '移动端用户',
    description: '主要使用移动设备访问的用户',
    color: '#06b6d4',
    category: '设备类型',
    userCount: 9876,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-01 11:20:00'
  },
  {
    id: '7',
    name: '问题用户',
    description: '存在违规行为的用户',
    color: '#dc2626',
    category: '用户状态',
    userCount: 23,
    status: 'disabled',
    createTime: '2024-02-01 00:00:00',
    updateTime: '2024-12-22 08:30:00'
  },
  {
    id: '8',
    name: '企业用户',
    description: '企业认证用户',
    color: '#059669',
    category: '用户类型',
    userCount: 567,
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-12-18 13:15:00'
  }
]);

// 搜索和筛选
const searchQuery = ref('');
const selectedCategory = ref<string | null>(null);
const selectedStatus = ref<string | null>(null);

// 选中的行
const checkedRowKeys = ref<DataTableRowKey[]>([]);

// 显示创建/编辑标签模态框
const showTagModal = ref(false);
const editingTag = ref<TagInfo | null>(null);

// 标签表单数据
const tagForm = ref({
  name: '',
  description: '',
  color: '#3b82f6',
  category: ''
});

// 分类选项
const categoryOptions = computed(() => [
  { label: '用户状态', value: '用户状态' },
  { label: '消费行为', value: '消费行为' },
  { label: '地域分布', value: '地域分布' },
  { label: '设备类型', value: '设备类型' },
  { label: '用户类型', value: '用户类型' }
]);

// 状态选项
const statusOptions = computed(() => [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'disabled' }
]);

// 过滤后的标签数据
const filteredTags = computed(() => {
  let result = tags.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(tag => 
      tag.name.toLowerCase().includes(query) ||
      tag.description.toLowerCase().includes(query)
    );
  }
  
  if (selectedCategory.value) {
    result = result.filter(tag => tag.category === selectedCategory.value);
  }
  
  if (selectedStatus.value) {
    result = result.filter(tag => tag.status === selectedStatus.value);
  }
  
  return result;
});

// 按分类分组的标签
const tagsByCategory = computed(() => {
  const grouped: Record<string, TagInfo[]> = {};
  filteredTags.value.forEach(tag => {
    if (!grouped[tag.category]) {
      grouped[tag.category] = [];
    }
    grouped[tag.category].push(tag);
  });
  return grouped;
});

// 表格列定义
const columns = computed(() => [
  {
    type: 'selection'
  },
  {
    title: '标签名称',
    key: 'name',
    width: 150,
    render: (row: any) => h('div', { class: 'flex items-center space-x-8px' }, [
      h(NTag, {
        color: { color: row.color, textColor: '#fff' },
        size: 'small'
      }, { default: () => row.name })
    ])
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '分类',
    key: 'category',
    width: 120,
    render: (row: any) => {
      const option = categoryOptions.value.find(opt => opt.value === row.category);
      return h('span', option?.label || row.category);
    }
  },
  {
    title: '用户数量',
    key: 'userCount',
    width: 120,
    render: (row: any) => h('span', { class: 'font-medium text-primary' }, row.userCount.toLocaleString())
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row: any) => {
      const statusMap = {
        active: { type: 'success' as const, label: '启用' },
        disabled: { type: 'error' as const, label: '禁用' }
      };
      const config = statusMap[row.status];
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 160,
    render: (row: any) => row.updateTime.split(' ')[0]
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row: any) => {
      return h('div', { class: 'flex items-center space-x-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          onClick: () => handleViewUsers(row.id)
        }, { default: () => '查看用户' }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          text: true,
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => '确认删除此标签？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            text: true
          }, { default: () => '删除' })
        })
      ]);
    }
  }
]);

// 操作函数
function handleSearch() {
  // 搜索逻辑已在 computed 中处理
}

function handleReset() {
  searchQuery.value = '';
  selectedCategory.value = null;
  selectedStatus.value = null;
}

function handleAdd() {
  editingTag.value = null;
  tagForm.value = {
    name: '',
    description: '',
    color: '#3b82f6',
    category: ''
  };
  showTagModal.value = true;
}

function handleEdit(tag: TagInfo) {
  editingTag.value = tag;
  tagForm.value = {
    name: tag.name,
    description: tag.description,
    color: tag.color,
    category: tag.category
  };
  showTagModal.value = true;
}

function handleSave() {
  if (!tagForm.value.name.trim()) {
    message.error('标签名称不能为空');
    return;
  }

  if (!tagForm.value.category) {
    message.error('标签分类不能为空');
    return;
  }

  if (editingTag.value) {
    // 编辑标签
    const index = tags.value.findIndex(t => t.id === editingTag.value!.id);
    if (index > -1) {
      tags.value[index] = {
        ...tags.value[index],
        ...tagForm.value,
        updateTime: new Date().toLocaleString()
      };
      message.success('更新标签成功');
    }
  } else {
    // 新增标签
    const newTag: TagInfo = {
      id: Date.now().toString(),
      ...tagForm.value,
      userCount: 0,
      status: 'active',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    };
    tags.value.push(newTag);
    message.success('创建标签成功');
  }

  showTagModal.value = false;
}

function handleDelete(id: string) {
  const index = tags.value.findIndex(tag => tag.id === id);
  if (index > -1) {
    tags.value.splice(index, 1);
    message.success('删除成功');
  }
}

function handleViewUsers(id: string) {
  message.info('查看用户: ' + id);
}

function handleBatchEnable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请先选择标签');
    return;
  }
  message.success('批量启用成功');
}

function handleBatchDisable() {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请先选择标签');
    return;
  }
  message.success('批量禁用成功');
}

function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    message.warning('请先选择标签');
    return;
  }

  tags.value = tags.value.filter(tag => !checkedRowKeys.value.includes(tag.id));
  checkedRowKeys.value = [];
  message.success('删除成功');
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ $t('page.userManagement.tags.title') }}
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            {{ $t('page.userManagement.tags.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ $t('page.userManagement.tags.createTag') }}
          </NButton>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <NCard :bordered="false" class="card-wrapper">
        <NSpace vertical :size="16">
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NGi span="24 s:24 m:8">
              <NInput
                v-model="searchQuery"
                placeholder="请输入标签名称搜索"
                clearable
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <SvgIcon icon="mdi:magnify" />
                </template>
              </NInput>
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedCategory"
                placeholder="选择分类"
                :options="categoryOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSelect
                v-model="selectedStatus"
                placeholder="选择状态"
                :options="statusOptions"
                clearable
              />
            </NGi>
            <NGi span="24 s:12 m:4">
              <NSpace>
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="mdi:magnify" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <SvgIcon icon="mdi:refresh" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
              </NSpace>
            </NGi>
          </NGrid>
          
          <!-- 批量操作 -->
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-12px">
              <span class="text-14px text-gray-600">
                {{ $t('page.userManagement.tags.selectedCount', { count: checkedRowKeys.length }) }}
              </span>
              <NButton 
                size="small" 
                type="success"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchEnable"
              >
                {{ $t('page.userManagement.tags.batchEnable') }}
              </NButton>
              <NButton 
                size="small" 
                type="warning"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDisable"
              >
                {{ $t('page.userManagement.tags.batchDisable') }}
              </NButton>
              <NButton 
                size="small" 
                type="error"
                :disabled="checkedRowKeys.length === 0"
                @click="handleBatchDelete"
              >
                {{ $t('page.userManagement.tags.batchDelete') }}
              </NButton>
            </div>
            <div class="text-14px text-gray-600">
              {{ $t('page.userManagement.tags.totalCount', { total: filteredTags.length }) }}
            </div>
          </div>
        </NSpace>
      </NCard>

      <!-- 标签分类展示 -->
      <div v-if="Object.keys(tagsByCategory).length > 0" class="space-y-16px">
        <div v-for="(categoryTags, category) in tagsByCategory" :key="category">
          <NCard :bordered="false" class="card-wrapper">
            <template #header>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <SvgIcon icon="mdi:tag-multiple" class="text-20px mr-8px text-primary" />
                  <span class="text-16px font-medium">
                    {{ categoryOptions.find(opt => opt.value === category)?.label || category }}
                  </span>
                  <NBadge :value="categoryTags.length" class="ml-8px" />
                </div>
              </div>
            </template>
            
            <div class="flex flex-wrap gap-12px">
              <div 
                v-for="tag in categoryTags" 
                :key="tag.id"
                class="flex items-center space-x-8px p-12px bg-gray-50 dark:bg-gray-800 rd-8px hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                @click="handleEdit(tag)"
              >
                <NTag 
                  :color="{ color: tag.color, textColor: '#fff' }"
                  size="medium"
                >
                  {{ tag.name }}
                </NTag>
                <div class="text-12px text-gray-500">
                  {{ tag.userCount.toLocaleString() }} {{ $t('page.userManagement.tags.users') }}
                </div>
                <NTag 
                  :type="tag.status === 'active' ? 'success' : 'error'"
                  size="small"
                >
                  {{ tag.status === 'active' ? $t('page.userManagement.tags.status.active') : $t('page.userManagement.tags.status.disabled') }}
                </NTag>
              </div>
            </div>
          </NCard>
        </div>
      </div>

      <!-- 标签列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <span class="text-16px font-medium">{{ $t('page.userManagement.tags.tagList') }}</span>
        </template>
        
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="filteredTags"
          :row-key="(row: TagInfo) => row.id"
          :scroll-x="1200"
          flex-height
          style="min-height: 400px"
        />
      </NCard>
    </NSpace>

    <!-- 创建/编辑标签模态框 -->
    <NModal v-model:show="showTagModal" preset="dialog" :title="editingTag ? $t('page.userManagement.tags.editTag') : $t('page.userManagement.tags.createTag')">
      <NSpace vertical :size="16">
        <NFormItem :label="$t('page.userManagement.tags.form.name')" required>
          <NInput v-model:value="tagForm.name" :placeholder="$t('page.userManagement.tags.form.namePlaceholder')" />
        </NFormItem>
        
        <NFormItem :label="$t('page.userManagement.tags.form.description')">
          <NInput
            v-model:value="tagForm.description"
            type="textarea"
            :placeholder="$t('page.userManagement.tags.form.descriptionPlaceholder')"
            :rows="3"
          />
        </NFormItem>
        
        <NFormItem :label="$t('page.userManagement.tags.form.category')" required>
          <NSelect
            v-model:value="tagForm.category"
            :placeholder="$t('page.userManagement.tags.form.categoryPlaceholder')"
            :options="categoryOptions"
          />
        </NFormItem>
        
        <NFormItem :label="$t('page.userManagement.tags.form.color')">
          <NColorPicker v-model:value="tagForm.color" />
        </NFormItem>
      </NSpace>
      
      <template #action>
        <NSpace>
          <NButton @click="showTagModal = false">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSave">{{ $t('common.save') }}</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped></style>
