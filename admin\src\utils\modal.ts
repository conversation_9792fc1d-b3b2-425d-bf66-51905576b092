/*
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
*/

import { computed } from 'vue';

/**
 * 弹窗尺寸类型
 */
export type ModalSize = 'small' | 'medium' | 'large' | 'extra-large' | 'full';

/**
 * 弹窗尺寸配置
 */
export interface ModalSizeConfig {
  /** 桌面端宽度 */
  desktop: string;
  /** 平板端宽度 */
  tablet: string;
  /** 移动端宽度 */
  mobile: string;
  /** 最大宽度 */
  maxWidth: string;
}

/**
 * 预定义的弹窗尺寸配置
 */
export const MODAL_SIZE_CONFIGS: Record<ModalSize, ModalSizeConfig> = {
  small: {
    desktop: '500px',
    tablet: '80vw',
    mobile: '95vw',
    maxWidth: '500px'
  },
  medium: {
    desktop: '700px',
    tablet: '85vw',
    mobile: '95vw',
    maxWidth: '700px'
  },
  large: {
    desktop: '900px',
    tablet: '90vw',
    mobile: '95vw',
    maxWidth: '900px'
  },
  'extra-large': {
    desktop: '1200px',
    tablet: '95vw',
    mobile: '95vw',
    maxWidth: '1200px'
  },
  full: {
    desktop: '95vw',
    tablet: '95vw',
    mobile: '95vw',
    maxWidth: '95vw'
  }
};

/**
 * 获取响应式弹窗类名
 * @param size 弹窗尺寸
 * @returns 响应式CSS类名
 */
export function getModalClass(size: ModalSize = 'medium'): string {
  const config = MODAL_SIZE_CONFIGS[size];
  
  // 使用UnoCSS的响应式类名
  const classes = [
    // 移动端 (< 640px)
    `w-[${config.mobile}]`,
    // 平板端 (>= 768px)
    `md:w-[${config.tablet}]`,
    // 桌面端 (>= 1024px)
    `lg:w-[${config.desktop}]`,
    // 最大宽度限制
    `max-w-[${config.maxWidth}]`,
    // 确保在小屏幕上不会超出视口
    'max-w-[95vw]'
  ];
  
  return classes.join(' ');
}

/**
 * 获取响应式弹窗宽度（用于计算属性）
 * @param size 弹窗尺寸
 * @returns 响应式宽度计算属性
 */
export function useModalWidth(size: ModalSize = 'medium') {
  return computed(() => {
    if (typeof window === 'undefined') {
      return MODAL_SIZE_CONFIGS[size].desktop;
    }
    
    const screenWidth = window.innerWidth;
    const config = MODAL_SIZE_CONFIGS[size];
    
    // 移动端
    if (screenWidth < 768) {
      return config.mobile;
    }
    // 平板端
    if (screenWidth < 1024) {
      return config.tablet;
    }
    // 桌面端
    return config.desktop;
  });
}

/**
 * 获取抽屉宽度（用于NDrawer组件）
 * @param size 抽屉尺寸
 * @returns 响应式宽度计算属性
 */
export function useDrawerWidth(size: ModalSize = 'large') {
  return computed(() => {
    if (typeof window === 'undefined') {
      return parseInt(MODAL_SIZE_CONFIGS[size].desktop);
    }
    
    const screenWidth = window.innerWidth;
    const config = MODAL_SIZE_CONFIGS[size];
    
    // 移动端 - 使用几乎全屏
    if (screenWidth < 768) {
      return '95%';
    }
    // 平板端
    if (screenWidth < 1024) {
      return config.tablet;
    }
    // 桌面端 - 返回数值类型
    return parseInt(config.desktop);
  });
}

/**
 * 弹窗内容类型配置
 */
export const MODAL_CONTENT_CONFIGS = {
  // 简单表单 (2-4个字段)
  simpleForm: 'medium',
  // 复杂表单 (5-8个字段)
  complexForm: 'large',
  // 超复杂表单 (8+个字段)
  extraComplexForm: 'extra-large',
  // 详情查看
  detail: 'large',
  // 确认对话框
  confirm: 'small',
  // 图片预览
  imagePreview: 'large',
  // 数据导入导出
  dataImport: 'large'
} as const;

/**
 * 根据内容类型获取推荐的弹窗尺寸
 * @param contentType 内容类型
 * @returns 弹窗尺寸
 */
export function getRecommendedModalSize(contentType: keyof typeof MODAL_CONTENT_CONFIGS): ModalSize {
  return MODAL_CONTENT_CONFIGS[contentType] as ModalSize;
}
