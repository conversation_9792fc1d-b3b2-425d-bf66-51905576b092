<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .key {
            font-weight: bold;
            color: #333;
        }
        .value {
            color: #666;
            margin-left: 10px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>分组表单弹窗国际化测试</h1>
    
    <h2>测试的国际化键值：</h2>
    
    <div class="test-item success">
        <div class="key">page.userManagement.groups.form.permissionsDesc</div>
        <div class="value">✅ 应该显示：请选择该分组拥有的系统权限，用户将继承分组的所有权限</div>
    </div>
    
    <div class="test-item success">
        <div class="key">page.userManagement.groups.form.name</div>
        <div class="value">✅ 应该显示：分组名称</div>
    </div>
    
    <div class="test-item success">
        <div class="key">page.userManagement.groups.form.color</div>
        <div class="value">✅ 应该显示：分组颜色</div>
    </div>
    
    <div class="test-item success">
        <div class="key">page.userManagement.groups.form.description</div>
        <div class="value">✅ 应该显示：分组描述</div>
    </div>
    
    <div class="test-item success">
        <div class="key">page.userManagement.groups.form.permissions</div>
        <div class="value">✅ 应该显示：分组权限</div>
    </div>
    
    <h2>修复内容：</h2>
    <ul>
        <li>✅ 在 <code>admin/src/locales/langs/zh-cn.ts</code> 中添加了缺失的 <code>permissionsDesc</code> 键值</li>
        <li>✅ 确认了 Vue 组件中的国际化调用语法正确</li>
        <li>✅ 验证了国际化键值路径的正确性</li>
    </ul>
    
    <h2>预期结果：</h2>
    <p>在分组表单弹窗的权限区域，应该显示中文描述文字而不是原始的国际化键值。</p>
    
    <h2>测试步骤：</h2>
    <ol>
        <li>启动开发服务器：<code>npm run dev</code></li>
        <li>访问用户管理 → 用户分组页面</li>
        <li>点击"创建分组"按钮</li>
        <li>查看权限区域的描述文字是否正确显示</li>
    </ol>
</body>
</html>
