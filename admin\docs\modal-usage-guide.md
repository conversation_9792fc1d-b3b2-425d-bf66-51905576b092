# FutureShop 弹窗尺寸使用指南

## 概述

本指南介绍了FutureShop电商系统后台管理中弹窗组件的正确使用方法，包括尺寸设置、响应式设计和最佳实践。

## 弹窗尺寸类型

### 1. 预定义尺寸类

我们提供了5种预定义的弹窗尺寸：

| 尺寸类型 | CSS类名 | 桌面端宽度 | 平板端宽度 | 移动端宽度 | 适用场景 |
|---------|---------|-----------|-----------|-----------|----------|
| 小尺寸 | `modal-small` | 500px | 80vw | 95vw | 确认对话框、简单表单 |
| 中等尺寸 | `modal-medium` | 700px | 85vw | 95vw | 一般表单、基础编辑 |
| 大尺寸 | `modal-large` | 900px | 90vw | 95vw | 复杂表单、详情查看 |
| 超大尺寸 | `modal-extra-large` | 1200px | 95vw | 95vw | 超复杂表单、数据导入导出 |
| 全屏 | `modal-full` | 95vw | 95vw | 95vw | 数据表格、图片预览 |

### 2. 特殊用途类

| CSS类名 | 用途 | 特点 |
|---------|------|------|
| `modal-form` | 表单弹窗 | 优化表单项间距和布局 |
| `modal-confirm` | 确认对话框 | 居中布局，紧凑设计 |
| `modal-detail-view` | 详情查看 | 支持滚动，适合长内容 |
| `modal-image-preview` | 图片预览 | 固定比例，居中显示 |
| `modal-data-table` | 数据表格 | 全屏显示，固定高度 |

## 使用方法

### 1. 基础用法

```vue
<template>
  <!-- 中等尺寸的表单弹窗 -->
  <NModal 
    v-model:show="showModal" 
    preset="dialog" 
    title="编辑用户"
    class="modal-medium modal-form"
  >
    <NForm>
      <!-- 表单内容 -->
    </NForm>
  </NModal>
</template>
```

### 2. 使用工具函数

```vue
<script setup lang="ts">
import { getModalClass, useDrawerWidth, getRecommendedModalSize } from '@/utils/modal';

// 方法1：使用推荐尺寸
const modalClass = getModalClass(getRecommendedModalSize('complexForm'));

// 方法2：直接指定尺寸
const modalClass2 = getModalClass('large');

// 抽屉宽度
const drawerWidth = useDrawerWidth('extra-large');
</script>

<template>
  <NModal 
    v-model:show="showModal" 
    preset="dialog" 
    title="复杂表单"
    :class="modalClass"
  >
    <!-- 内容 -->
  </NModal>
  
  <NDrawer v-model:show="showDrawer" :width="drawerWidth">
    <!-- 抽屉内容 -->
  </NDrawer>
</template>
```

### 3. 内容类型推荐

```typescript
// 根据内容类型获取推荐尺寸
const contentTypes = {
  simpleForm: 'medium',        // 2-4个字段的简单表单
  complexForm: 'large',        // 5-8个字段的复杂表单
  extraComplexForm: 'extra-large', // 8+个字段的超复杂表单
  detail: 'large',             // 详情查看
  confirm: 'small',            // 确认对话框
  imagePreview: 'large',       // 图片预览
  dataImport: 'large'          // 数据导入导出
};
```

## 响应式设计

### 1. 自动适配

所有弹窗都会根据屏幕尺寸自动调整：

- **移动端 (< 768px)**: 所有弹窗都使用 95vw 宽度
- **平板端 (768px - 1024px)**: 使用相应的平板端宽度
- **桌面端 (> 1024px)**: 使用完整的桌面端宽度

### 2. 断点说明

```css
/* 移动端 */
@media (max-width: 768px) {
  .modal-small,
  .modal-medium,
  .modal-large,
  .modal-extra-large {
    width: 95vw !important;
    max-width: 95vw !important;
  }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .modal-large {
    width: 85vw;
  }
  
  .modal-extra-large {
    width: 90vw;
  }
}
```

## 最佳实践

### 1. 选择合适的尺寸

- **简单表单 (2-4个字段)**: 使用 `modal-medium`
- **复杂表单 (5-8个字段)**: 使用 `modal-large`
- **超复杂表单 (8+个字段)**: 使用 `modal-extra-large`
- **确认对话框**: 使用 `modal-small modal-confirm`
- **详情查看**: 使用 `modal-large modal-detail-view`

### 2. 组合使用类名

```vue
<!-- 表单弹窗 -->
<NModal class="modal-medium modal-form">

<!-- 确认对话框 -->
<NModal class="modal-small modal-confirm">

<!-- 详情查看 -->
<NModal class="modal-large modal-detail-view">

<!-- 数据表格 -->
<NModal class="modal-extra-large modal-data-table">
```

### 3. 避免的做法

❌ **不要使用内联样式设置宽度**
```vue
<!-- 错误 -->
<NModal style="width: 800px;">
```

❌ **不要使用固定的像素值类名**
```vue
<!-- 错误 -->
<NModal class="w-800px">
```

✅ **正确的做法**
```vue
<!-- 正确 -->
<NModal class="modal-large modal-form">
```

## 自定义尺寸

如果预定义尺寸不满足需求，可以创建自定义尺寸：

```css
/* 自定义尺寸 */
.modal-custom {
  width: 850px;
  max-width: 90vw;
}

@media (max-width: 768px) {
  .modal-custom {
    width: 95vw !important;
    max-width: 95vw !important;
  }
}
```

## 常见问题

### Q: 为什么弹窗在移动端显示不正常？
A: 确保使用了响应式类名，避免使用固定像素值。

### Q: 如何让弹窗内容支持滚动？
A: 使用 `modal-detail-view` 类，它会自动处理内容溢出。

### Q: 表单在弹窗中布局混乱怎么办？
A: 使用 `modal-form` 类，它会优化表单项的间距和布局。

### Q: 抽屉宽度如何设置？
A: 使用 `useDrawerWidth()` 函数，它会返回响应式的宽度值。

## 更新日志

- **v1.0.0**: 初始版本，提供基础弹窗尺寸类
- **v1.1.0**: 添加工具函数和响应式支持
- **v1.2.0**: 增加特殊用途类和最佳实践指南
