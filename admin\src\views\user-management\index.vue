<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';

defineOptions({
  name: 'UserManagement'
});

const router = useRouter();

interface ModuleCard {
  key: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  route: string;
  stats?: {
    label: string;
    value: string | number;
  };
}

const moduleCards = computed<ModuleCard[]>(() => [
  {
    key: 'statistics',
    title: '用户统计',
    description: '查看用户增长趋势、来源分析和地域分布等统计数据',
    icon: 'mdi:chart-line',
    color: 'bg-gradient-to-br from-blue-500 to-blue-600',
    route: '/user-management/statistics',
    stats: {
      label: '总用户数',
      value: '15,678'
    }
  },
  {
    key: 'users',
    title: '用户管理',
    description: '查看和管理所有系统用户信息',
    icon: 'mdi:account-group',
    color: 'bg-gradient-to-br from-green-500 to-green-600',
    route: '/user-management/users',
    stats: {
      label: '活跃用户',
      value: '12,345'
    }
  },
  {
    key: 'groups',
    title: '用户分组',
    description: '创建和管理用户分组，配置分组权限',
    icon: 'mdi:account-multiple-outline',
    color: 'bg-gradient-to-br from-purple-500 to-purple-600',
    route: '/user-management/groups',
    stats: {
      label: '分组总数',
      value: '8'
    }
  },
  {
    key: 'tags',
    title: '用户标签',
    description: '创建和管理用户标签，为用户分类打标',
    icon: 'mdi:tag-multiple',
    color: 'bg-gradient-to-br from-orange-500 to-orange-600',
    route: '/user-management/tags',
    stats: {
      label: '标签总数',
      value: '24'
    }
  },
  {
    key: 'levels',
    title: '用户等级',
    description: '设置用户等级体系和升级规则',
    icon: 'mdi:star-circle',
    color: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
    route: '/user-management/levels',
    stats: {
      label: '等级总数',
      value: '6'
    }
  },
  {
    key: 'settings',
    title: '用户配置',
    description: '配置用户注册、权限和系统参数',
    icon: 'mdi:cog',
    color: 'bg-gradient-to-br from-gray-500 to-gray-600',
    route: '/user-management/settings'
  }
]);

// 快速统计数据
const quickStats = computed(() => [
  {
    label: '总用户数',
    value: '15,678',
    icon: 'mdi:account-group',
    color: 'text-blue-500',
    growth: '+12.5%'
  },
  {
    label: '活跃用户',
    value: '12,345',
    icon: 'mdi:account-check',
    color: 'text-green-500',
    growth: '+8.3%'
  },
  {
    label: '今日新增',
    value: '156',
    icon: 'mdi:account-plus',
    color: 'text-orange-500',
    growth: '+15.2%'
  },
  {
    label: 'VIP用户',
    value: '2,345',
    icon: 'mdi:crown',
    color: 'text-yellow-500',
    growth: '+5.7%'
  }
]);

function navigateToModule(route: string) {
  router.push(route);
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="24" class="p-24px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-28px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            {{ $t('page.userManagement.title') }}
          </h1>
          <p class="text-16px text-gray-600 dark:text-gray-400">
            {{ $t('page.userManagement.description') }}
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" size="large">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            {{ $t('page.userManagement.addUser') }}
          </NButton>
          <NButton size="large">
            <template #icon>
              <SvgIcon icon="mdi:download" />
            </template>
            {{ $t('page.userManagement.export') }}
          </NButton>
        </div>
      </div>

      <!-- 快速统计 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center">
            <SvgIcon icon="mdi:speedometer" class="text-20px mr-8px text-primary" />
            <span class="text-18px font-medium">{{ $t('page.userManagement.overview.title') }}</span>
          </div>
        </template>
        
        <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi v-for="stat in quickStats" :key="stat.label" span="24 s:12 m:6">
            <div class="p-20px bg-gray-50 dark:bg-gray-800 rd-12px">
              <div class="flex items-center justify-between mb-12px">
                <SvgIcon :icon="stat.icon" class="text-24px" :class="stat.color" />
                <span class="text-12px text-green-500 font-medium">{{ stat.growth }}</span>
              </div>
              <div class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-4px">
                {{ stat.value }}
              </div>
              <div class="text-14px text-gray-600 dark:text-gray-400">
                {{ stat.label }}
              </div>
            </div>
          </NGi>
        </NGrid>
      </NCard>

      <!-- 功能模块 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center">
            <SvgIcon icon="mdi:view-module" class="text-20px mr-8px text-primary" />
            <span class="text-18px font-medium">功能模块</span>
          </div>
        </template>

        <NGrid :x-gap="20" :y-gap="20" responsive="screen" item-responsive>
          <NGi v-for="module in moduleCards" :key="module.key" span="24 s:12 m:8">
            <div 
              class="relative overflow-hidden rd-16px cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg"
              @click="navigateToModule(module.route)"
            >
              <div :class="module.color" class="p-24px text-white">
                <div class="flex items-start justify-between mb-16px">
                  <SvgIcon :icon="module.icon" class="text-32px" />
                  <div v-if="module.stats" class="text-right">
                    <div class="text-20px font-bold">{{ module.stats.value }}</div>
                    <div class="text-12px opacity-80">{{ module.stats.label }}</div>
                  </div>
                </div>
                <h3 class="text-18px font-bold mb-8px">{{ module.title }}</h3>
                <p class="text-14px opacity-90 line-height-relaxed">{{ module.description }}</p>
                
                <!-- 进入按钮 -->
                <div class="flex items-center justify-end mt-16px">
                  <div class="flex items-center text-14px font-medium">
                    进入模块
                    <SvgIcon icon="mdi:arrow-right" class="text-16px ml-4px" />
                  </div>
                </div>
              </div>
            </div>
          </NGi>
        </NGrid>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped>
.line-height-relaxed {
  line-height: 1.6;
}
</style>
