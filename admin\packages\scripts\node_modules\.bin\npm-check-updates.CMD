@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules\npm-check-updates\build\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules\npm-check-updates\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules\npm-check-updates\build\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules\npm-check-updates\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\npm-check-updates@18.0.1\node_modules;D:\CodeFile\codeproject\futureshop\admin\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\npm-check-updates\build\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\npm-check-updates\build\cli.js" %*
)
