---
type: "always_apply"
---

# SoybeanAdmin 开发安全规则

## 🛡️ 代码安全保障机制

### 1. 深度理解现有框架
在修改任何代码之前，我会：
- 使用 `codebase-retrieval` 工具深入了解现有代码结构
- 分析框架的核心组件、约定和最佳实践
- 查看已有的代码模式和风格

### 2. 严格遵循SoybeanAdmin框架规范

#### 2.1 技术栈约束
- **前端框架**: Vue 3.5.17 + TypeScript 5.8.3 + script setup语法
- **构建工具**: Vite 7.0.4
- **UI组件库**: Naive UI 2.42.0（禁止引入其他UI库）
- **CSS框架**: UnoCSS 66.3.3（原子化CSS）
- **状态管理**: Pinia 3.0.3（Composition API风格）
- **路由**: Vue Router 4.5.1 + Elegant Router（自动文件路由）
- **国际化**: Vue I18n 11.1.9
- **工具库**: VueUse 13.5.0, dayjs 1.11.13

#### 2.2 目录结构约束
```
admin/src/
├── components/          # 组件库（common/custom/advanced）
├── views/              # 页面组件（按模块分组）
├── layouts/            # 布局组件（禁止修改）
├── router/             # 路由配置（自动生成，禁止手动修改）
├── store/              # Pinia状态管理（按模块分组）
├── service/            # API服务（统一封装）
├── styles/             # 样式文件（优先使用UnoCSS）
├── typings/            # TypeScript类型定义
├── locales/            # 国际化文件
├── utils/              # 工具函数
└── theme/              # 主题配置（禁止随意修改）
```

### 3. 命名规范（严格遵循SoybeanJS规范）

#### 3.1 文件和文件夹命名
- **统一使用小写 + 连字符**: `user-list.vue`, `order-detail/`
- **路由文件夹**: 一级路由 `demo`, 二级路由 `demo_child`, 三级路由 `demo_child_child`

#### 3.2 Vue组件命名
- **组件名**: PascalCase - `<UserList>`, `<OrderDetail>`
- **Iconify图标**: kebab-case - `<icon-mdi-emoticon>`
- **defineOptions**: 必须定义组件名

#### 3.3 变量和函数命名
- **变量/函数**: camelCase - `userName`, `getUserInfo()`
- **常量**: UPPER_SNAKE_CASE - `MAX_COUNT`, `API_BASE_URL`
- **类型/接口**: PascalCase - `UserInfo`, `ApiResponse`
- **请求函数**: 以`fetch`开头 - `fetchUserList()`, `fetchOrderDetail()`

### 4. Vue组件开发规范

#### 4.1 SFC结构顺序（严格按照官方规范）
```vue
<script setup lang="ts">
// 1. import导入（按依赖顺序）
import { ref } from 'vue';
import type { Ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAppStore } from '@/store/modules/app';

// 2. defineOptions
defineOptions({
  name: 'ComponentName'
});

// 3. Props类型定义
interface Props {
  prop1: string;
  prop2?: number;
}

// 4. defineProps
const props = defineProps<Props>();

// 5. Emits类型定义
interface Emits {
  submit: [data: FormData];
  cancel: [];
}

// 6. defineEmits
const emit = defineEmits<Emits>();

// 7. hooks导入
const router = useRouter();
const appStore = useAppStore();

// 8. 组件逻辑
const loading = ref(false);

// 9. init函数
async function init() {
  // 初始化逻辑
}

// 10. watch/watchEffect
// 11. 生命周期
// 12. defineExpose
</script>

<template>
  <!-- 只能有一个根元素（支持Transition动画） -->
  <div class="container">
    <!-- 内容 -->
  </div>
</template>

<style scoped>
/* 优先使用UnoCSS，必要时才写CSS */
</style>
```

#### 4.2 模板规范
- **只能有一个根元素**（支持页面过渡动画）
- **使用Naive UI组件**: `<NCard>`, `<NButton>`, `<NSpace>` 等
- **响应式设计**: 使用 `<NGrid>`, `<NGi>` 进行布局
- **国际化**: 使用 `$t('key')` 或 `{{ $t('key') }}`

### 5. 路由系统规范

#### 5.1 路由创建（基于Elegant Router）
- **自动生成**: 路由由文件结构自动生成，禁止手动修改 `router/elegant/` 目录
- **文件结构**: `views/user/list/index.vue` → 路由 `/user/list`
- **路由命名**: 一级 `user`, 二级 `user_list`, 三级 `user_list_detail`
- **命令创建**: 使用 `pnpm gen-route` 创建路由

#### 5.2 路由元信息
```typescript
// 在路由组件中定义meta
defineOptions({
  name: 'UserList',
  meta: {
    title: '用户列表',
    i18nKey: 'route.user.list',
    icon: 'mdi:account-group',
    order: 1,
    keepAlive: true,
    roles: ['admin', 'user']
  }
});
```

### 6. 状态管理规范（Pinia）

#### 6.1 Store结构
```typescript
// store/modules/user/index.ts
import { defineStore } from 'pinia';
import { useBoolean } from '@sa/hooks';

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null);
  const { bool: loading, setTrue: startLoading, setFalse: endLoading } = useBoolean();

  // 计算属性
  const isLogin = computed(() => !!userInfo.value);

  // 方法
  async function login(credentials: LoginForm) {
    startLoading();
    try {
      const { data } = await fetchLogin(credentials);
      userInfo.value = data;
    } finally {
      endLoading();
    }
  }

  return {
    userInfo,
    loading,
    isLogin,
    login
  };
});
```

### 7. 样式系统规范

#### 7.1 UnoCSS原子化CSS
```vue
<template>
  <!-- 布局类 -->
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden">
    <!-- 响应式类 -->
    <div class="lt-sm:overflow-auto sm:flex-1-hidden">
      <!-- 主题色类 -->
      <NCard class="card-wrapper bg-primary text-primary">
        <!-- 内容 -->
      </NCard>
    </div>
  </div>
</template>
```

#### 7.2 响应式断点
- `lt-sm:` - 小于 sm 断点
- `sm:` - sm 断点及以上
- `m:` - medium 断点
- `l:` - large 断点

### 8. 图标使用规范

#### 8.1 Iconify图标
```vue
<!-- 静态使用 -->
<icon-mdi-emoticon class="text-24px text-red" />

<!-- 动态使用 -->
<SvgIcon icon="mdi-emoticon" class="text-24px" />

<!-- 本地SVG -->
<SvgIcon local-icon="custom-icon" />
```

#### 8.2 图标来源
- **Iconify**: https://icones.js.org/
- **本地SVG**: `src/assets/svg-icon/` 目录

### 9. API请求规范

#### 9.1 请求函数
```typescript
// service/api/user.ts
import { request } from '../request';

export function fetchUserList(params: UserListParams) {
  return request<Api.User.UserList>({
    url: '/user/list',
    method: 'get',
    params
  });
}

export function fetchCreateUser(data: CreateUserData) {
  return request<Api.User.User>({
    url: '/user',
    method: 'post',
    data
  });
}
```

### 10. 禁止操作清单

#### 10.1 绝对禁止
- ❌ 修改 `router/elegant/` 自动生成的文件
- ❌ 修改 `layouts/` 核心布局组件
- ❌ 修改 `theme/` 主题配置文件
- ❌ 引入除Naive UI外的其他UI组件库
- ❌ 修改 `vite.config.ts`, `uno.config.ts` 等核心配置
- ❌ 修改 `package.json` 依赖（使用包管理器）
- ❌ 使用传统CSS而不是UnoCSS
- ❌ 在template中使用多个根元素

#### 10.2 谨慎操作
- ⚠️ 修改 `store/` 中的现有store
- ⚠️ 修改 `service/request/` 请求封装
- ⚠️ 修改 `typings/` 类型定义
- ⚠️ 修改 `locales/` 国际化配置

### 11. 开发流程规范

#### 11.1 新功能开发流程
1. **需求分析**: 明确功能需求和技术方案
2. **查看现有代码**: 了解相似功能的实现方式
3. **创建路由**: 使用 `pnpm gen-route` 创建页面
4. **开发组件**: 遵循Vue组件规范
5. **API对接**: 按照请求规范封装API
6. **状态管理**: 必要时创建Pinia store
7. **样式调整**: 使用UnoCSS原子类
8. **测试验证**: 确保功能正常

#### 11.2 修改现有功能流程
1. **深入了解**: 使用 `codebase-retrieval` 了解现有实现
2. **影响评估**: 评估修改对其他模块的影响
3. **小步修改**: 每次只修改必要的部分
4. **测试验证**: 确保修改不破坏现有功能

### 12. 质量保证

#### 12.1 代码检查
- **ESLint**: 遵循项目配置的ESLint规则
- **Prettier**: 使用项目配置的代码格式化
- **TypeScript**: 严格类型检查，无any类型
- **Vue**: 遵循Vue 3 Composition API最佳实践

#### 12.2 性能优化
- **懒加载**: 路由组件自动懒加载
- **缓存**: 合理使用 `keepAlive`
- **响应式**: 避免不必要的响应式数据
- **组件拆分**: 合理拆分大组件

### 13. 应急处理

#### 13.1 出现问题时
1. **立即停止**: 停止当前操作
2. **问题定位**: 使用开发工具定位问题
3. **回滚方案**: 使用Git回滚到稳定版本
4. **寻求帮助**: 向用户说明情况并寻求指导

#### 13.2 预防措施
- **小步提交**: 每个功能点完成后及时提交
- **备份重要文件**: 修改前备份关键配置
- **测试先行**: 修改后立即测试
- **文档记录**: 记录重要的修改和决策

