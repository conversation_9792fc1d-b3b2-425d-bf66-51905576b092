<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组表单数据回填 - ref方式修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fix-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .fix-item {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: white;
            border-radius: 4px;
        }
        .success {
            border-left-color: #28a745;
        }
        .warning {
            border-left-color: #ffc107;
        }
        .error {
            border-left-color: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 13px;
            overflow-x: auto;
        }
        h1, h2, h3 {
            color: #333;
        }
        h1 {
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        .icon {
            font-size: 18px;
            margin-right: 8px;
        }
        .success-icon { color: #28a745; }
        .warning-icon { color: #ffc107; }
        .error-icon { color: #dc3545; }
        .info-icon { color: #007bff; }
        .fix-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .fix-summary h3 {
            color: white;
            margin-top: 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        .before {
            background: #fff5f5;
            border: 1px solid #fed7d7;
        }
        .after {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
        }
        .test-checklist {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .test-checklist ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="icon">🔧</span>分组表单数据回填 - ref方式修复</h1>
        
        <div class="fix-summary">
            <h3><span class="icon">💡</span>新的修复方案</h3>
            <p>将formData从reactive改为ref，这可能解决Naive UI组件与reactive对象的兼容性问题。ref方式提供了更明确的响应式控制，有助于确保表单组件正确响应数据变化。</p>
        </div>

        <div class="fix-section">
            <h2><span class="icon info-icon">🔄</span>核心修改</h2>
            
            <div class="comparison">
                <div class="before">
                    <h4><span class="icon error-icon">❌</span>修改前 (reactive)</h4>
                    <div class="code">
// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [] as string[],
  status: 'active' as 'active' | 'disabled'
});

// 使用方式
formData.name = 'new value';
Object.assign(formData, newData);
                    </div>
                </div>
                <div class="after">
                    <h4><span class="icon success-icon">✅</span>修改后 (ref)</h4>
                    <div class="code">
// 表单数据
const formData = ref({
  id: '',
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [] as string[],
  status: 'active' as 'active' | 'disabled'
});

// 使用方式
formData.value.name = 'new value';
formData.value = newData;
                    </div>
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h2><span class="icon success-icon">🛠️</span>具体修改内容</h2>
            
            <div class="fix-item success">
                <h3>1. 表单数据定义修改</h3>
                <div class="code">
// 修改前
const formData = reactive({
  id: '',
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [] as string[],
  status: 'active' as 'active' | 'disabled'
});

// 修改后
const formData = ref({
  id: '',
  name: '',
  description: '',
  color: '#4ecdc4',
  permissions: [] as string[],
  status: 'active' as 'active' | 'disabled'
});
                </div>
            </div>

            <div class="fix-item success">
                <h3>2. 编辑函数修改</h3>
                <div class="code">
function handleEditGroup(id: string) {
  // 查找目标分组
  const group = groups.value.find(g => g.id === id);
  if (!group) {
    return;
  }
  
  // 设置编辑模式
  isEditMode.value = true;
  
  // 直接设置表单数据 - 关键修改
  formData.value = {
    id: group.id,
    name: group.name,
    description: group.description || '',
    color: group.color || '#4ecdc4',
    permissions: [...group.permissions],
    status: group.status || 'active'
  };
  
  // 显示弹窗
  showGroupModal.value = true;
  
  // 清除表单验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.restoreValidation();
    }
  });
}
                </div>
            </div>

            <div class="fix-item success">
                <h3>3. 克隆函数修改</h3>
                <div class="code">
function handleCloneGroup(id: string) {
  // 查找目标分组
  const group = groups.value.find(g => g.id === id);
  if (!group) {
    return;
  }
  
  // 设置为新增模式
  isEditMode.value = false;
  
  // 直接设置表单数据（克隆原分组信息） - 关键修改
  formData.value = {
    id: '',
    name: `${group.name} - 副本`,
    description: group.description || '',
    color: group.color || '#4ecdc4',
    permissions: [...group.permissions],
    status: group.status || 'active'
  };
  
  // 显示弹窗
  showGroupModal.value = true;
  
  // 清除表单验证状态
  nextTick(() => {
    if (formRef.value) {
      formRef.value.restoreValidation();
    }
  });
}
                </div>
            </div>

            <div class="fix-item success">
                <h3>4. 其他函数修改</h3>
                <div class="code">
// 重置表单
function resetForm() {
  formData.value.id = '';
  formData.value.name = '';
  formData.value.description = '';
  formData.value.color = '#4ecdc4';
  formData.value.permissions = [];
  formData.value.status = 'active';
  
  if (formRef.value) {
    formRef.value.restoreValidation();
  }
}

// 权限选择统计计算属性
const selectedPermissionsCount = computed(() => {
  return Array.isArray(formData.value.permissions) ? formData.value.permissions.length : 0;
});

// 权限变化处理函数
function handlePermissionsChange(value: string[]) {
  formData.value.permissions = value;
}
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h2><span class="icon warning-icon">🔍</span>为什么这样修改？</h2>
            
            <div class="fix-item">
                <h3>reactive vs ref 的区别</h3>
                <ul>
                    <li><strong>reactive</strong>：深度响应式，但对象属性的重新赋值可能不会触发更新</li>
                    <li><strong>ref</strong>：通过.value访问，整个对象的替换会触发完整的响应式更新</li>
                    <li><strong>Naive UI兼容性</strong>：某些UI组件可能对ref对象有更好的支持</li>
                    <li><strong>数据替换</strong>：formData.value = newData 会触发完整的重新渲染</li>
                </ul>
            </div>

            <div class="fix-item">
                <h3>关键优势</h3>
                <ul>
                    <li>✅ <strong>完整替换</strong>：formData.value = newData 确保所有字段都被更新</li>
                    <li>✅ <strong>响应式保证</strong>：ref的.value赋值会触发所有依赖的更新</li>
                    <li>✅ <strong>组件兼容</strong>：Naive UI组件能更好地检测到ref对象的变化</li>
                    <li>✅ <strong>调试友好</strong>：ref对象的变化更容易在开发工具中观察</li>
                </ul>
            </div>
        </div>

        <div class="fix-section">
            <h2><span class="icon warning-icon">🧪</span>测试验证</h2>
            
            <div class="test-checklist">
                <h3>立即测试清单</h3>
                <ul>
                    <li>□ <strong>编辑功能</strong>：点击编辑按钮，检查表单是否显示原分组信息</li>
                    <li>□ <strong>分组名称</strong>：验证名称字段是否正确显示</li>
                    <li>□ <strong>分组描述</strong>：验证描述字段是否正确显示</li>
                    <li>□ <strong>分组颜色</strong>：验证颜色选择器是否显示正确颜色</li>
                    <li>□ <strong>权限选择</strong>：验证权限复选框是否正确选中</li>
                    <li>□ <strong>权限统计</strong>：验证权限统计数字是否正确</li>
                    <li>□ <strong>克隆功能</strong>：点击克隆按钮，检查表单是否显示原分组信息+副本标识</li>
                    <li>□ <strong>保存功能</strong>：验证编辑和克隆保存是否正常工作</li>
                </ul>
            </div>
        </div>

        <div class="fix-section">
            <h2><span class="icon success-icon">🎯</span>预期效果</h2>
            
            <div class="fix-summary">
                <h3>修复后应该看到</h3>
                <p>✅ <strong>编辑功能</strong>：点击编辑后表单立即显示所有原分组信息</p>
                <p>✅ <strong>克隆功能</strong>：点击克隆后表单显示原分组信息且名称带"- 副本"</p>
                <p>✅ <strong>字段回填</strong>：所有表单字段（名称、描述、颜色、权限）都正确显示</p>
                <p>✅ <strong>权限选择</strong>：权限复选框正确选中，统计数字准确</p>
                <p>✅ <strong>响应式更新</strong>：表单组件能够正确响应数据变化</p>
            </div>
            
            <div class="fix-item success">
                <h3>如果仍然不工作</h3>
                <p>如果这次修复仍然不能解决问题，那么问题可能在于：</p>
                <ul>
                    <li>Naive UI组件的特殊实现方式</li>
                    <li>表单验证规则的干扰</li>
                    <li>组件生命周期的时序问题</li>
                    <li>需要考虑其他更深层的解决方案</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
