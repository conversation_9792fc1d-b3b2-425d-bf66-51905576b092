const local: App.I18n.Schema = {
  system: {
    title: 'FutureShop 管理系统',
    updateTitle: '系统版本更新通知',
    updateContent: '检测到系统有新版本发布，是否立即刷新页面？',
    updateConfirm: '立即刷新',
    updateCancel: '稍后再说'
  },
  common: {
    action: '操作',
    add: '新增',
    addSuccess: '添加成功',
    backToHome: '返回首页',
    batchDelete: '批量删除',
    cancel: '取消',
    close: '关闭',
    check: '勾选',
    expandColumn: '展开列',
    columnSetting: '列设置',
    config: '配置',
    confirm: '确认',
    delete: '删除',
    deleteSuccess: '删除成功',
    confirmDelete: '确认删除吗？',
    edit: '编辑',
    warning: '警告',
    error: '错误',
    index: '序号',
    keywordSearch: '请输入关键词搜索',
    logout: '退出登录',
    logoutConfirm: '确认退出登录吗？',
    lookForward: '敬请期待',
    modify: '修改',
    modifySuccess: '修改成功',
    more: '更多',
    noData: '无数据',
    operate: '操作',
    pleaseCheckValue: '请检查输入的值是否合法',
    refresh: '刷新',
    reset: '重置',
    search: '搜索',
    switch: '切换',
    tip: '提示',
    trigger: '触发',
    update: '更新',
    updateSuccess: '更新成功',
    userCenter: '个人中心',
    yesOrNo: {
      yes: '是',
      no: '否'
    }
  },
  request: {
    logout: '请求失败后登出用户',
    logoutMsg: '用户状态失效，请重新登录',
    logoutWithModal: '请求失败后弹出模态框再登出用户',
    logoutWithModalMsg: '用户状态失效，请重新登录',
    refreshToken: '请求的token已过期，刷新token',
    tokenExpired: 'token已过期'
  },
  theme: {
    themeSchema: {
      title: '主题模式',
      light: '亮色模式',
      dark: '暗黑模式',
      auto: '跟随系统'
    },
    grayscale: '灰色模式',
    colourWeakness: '色弱模式',
    layoutMode: {
      title: '布局模式',
      vertical: '左侧菜单模式',
      'vertical-mix': '左侧菜单混合模式',
      horizontal: '顶部菜单模式',
      'horizontal-mix': '顶部菜单混合模式',
      reverseHorizontalMix: '一级菜单与子级菜单位置反转'
    },
    recommendColor: '应用推荐算法的颜色',
    recommendColorDesc: '推荐颜色的算法参照',
    themeColor: {
      title: '主题颜色',
      primary: '主色',
      info: '信息色',
      success: '成功色',
      warning: '警告色',
      error: '错误色',
      followPrimary: '跟随主色'
    },
    scrollMode: {
      title: '滚动模式',
      wrapper: '外层滚动',
      content: '主体滚动'
    },
    page: {
      animate: '页面切换动画',
      mode: {
        title: '页面切换动画类型',
        'fade-slide': '滑动',
        fade: '淡入淡出',
        'fade-bottom': '底部消退',
        'fade-scale': '缩放消退',
        'zoom-fade': '渐变',
        'zoom-out': '闪现',
        none: '无'
      }
    },
    fixedHeaderAndTab: '固定头部和标签栏',
    header: {
      height: '头部高度',
      breadcrumb: {
        visible: '显示面包屑',
        showIcon: '显示面包屑图标'
      },
      multilingual: {
        visible: '显示多语言按钮'
      },
      globalSearch: {
        visible: '显示全局搜索按钮'
      }
    },
    tab: {
      visible: '显示标签栏',
      cache: '标签栏信息缓存',
      height: '标签栏高度',
      mode: {
        title: '标签栏风格',
        chrome: '谷歌风格',
        button: '按钮风格'
      }
    },
    sider: {
      inverted: '深色侧边栏',
      width: '侧边栏宽度',
      collapsedWidth: '侧边栏折叠宽度',
      mixWidth: '混合布局侧边栏宽度',
      mixCollapsedWidth: '混合布局侧边栏折叠宽度',
      mixChildMenuWidth: '混合布局子菜单宽度'
    },
    footer: {
      visible: '显示底部',
      fixed: '固定底部',
      height: '底部高度',
      right: '底部局右'
    },
    watermark: {
      visible: '显示全屏水印',
      text: '水印文本',
      enableUserName: '启用用户名水印'
    },
    themeDrawerTitle: '主题配置',
    pageFunTitle: '页面功能',
    resetCacheStrategy: {
      title: '重置缓存策略',
      close: '关闭页面',
      refresh: '刷新页面'
    },
    configOperation: {
      copyConfig: '复制配置',
      copySuccessMsg: '复制成功，请替换 src/theme/settings.ts 中的变量 themeSettings',
      resetConfig: '重置配置',
      resetSuccessMsg: '重置成功'
    }
  },
  route: {
    login: '登录',
    403: '无权限',
    404: '页面不存在',
    500: '服务器错误',
    'iframe-page': '外链页面',
    home: '首页',
    'user-management': '用户管理',
    'user-management_statistics': '用户统计',
    'user-management_users': '用户管理',
    'user-management_groups': '用户分组',
    'user-management_tags': '用户标签',
    'user-management_levels': '用户等级',
    'user-management_settings': '用户配置'
  },
  page: {
    login: {
      common: {
        loginOrRegister: '登录 / 注册',
        userNamePlaceholder: '请输入用户名',
        phonePlaceholder: '请输入手机号',
        codePlaceholder: '请输入验证码',
        passwordPlaceholder: '请输入密码',
        confirmPasswordPlaceholder: '请再次输入密码',
        codeLogin: '验证码登录',
        confirm: '确定',
        back: '返回',
        validateSuccess: '验证成功',
        loginSuccess: '登录成功',
        welcomeBack: '欢迎回来，{userName} ！'
      },
      pwdLogin: {
        title: '密码登录',
        rememberMe: '记住我',
        forgetPassword: '忘记密码？',
        register: '注册账号',
        otherAccountLogin: '其他账号登录',
        otherLoginMode: '其他登录方式',
        superAdmin: '超级管理员',
        admin: '管理员',
        user: '普通用户'
      },
      codeLogin: {
        title: '验证码登录',
        getCode: '获取验证码',
        reGetCode: '{time}秒后重新获取',
        sendCodeSuccess: '验证码发送成功',
        imageCodePlaceholder: '请输入图片验证码'
      },
      register: {
        title: '注册账号',
        agreement: '我已经仔细阅读并接受',
        protocol: '《用户协议》',
        policy: '《隐私权政策》'
      },
      resetPwd: {
        title: '重置密码'
      },
      bindWeChat: {
        title: '绑定微信'
      }
    },
    home: {
      branchDesc:
        '为了方便大家开发和更新合并，我们对main分支的代码进行了精简，只保留了首页菜单，其余内容已移至example分支进行维护。预览地址显示的内容即为example分支的内容。',
      welcome: '欢迎使用',
      dashboard: '管理后台',
      currentUser: '当前用户',
      userRole: '用户角色',
      lastLogin: '最后登录',
      todayOrders: '今日订单',
      todaySales: '今日销售额',
      todayUsers: '今日新用户',
      quickActions: '快捷操作',
      newOrder: '新建订单',
      productManagement: '商品管理',
      salesReport: '销售报表',
      roles: {
        admin: '管理员',
        manager: '经理',
        user: '普通用户',
        guest: '访客'
      },
      totalSales: '总销售额',
      totalOrders: '总订单数',
      totalUsers: '总用户数',
      totalProducts: '总商品数',
      vsLastMonth: '较上月',
      salesTrend: '销售趋势',
      salesAmount: '销售金额',
      orderAmount: '订单金额',
      recentOrders: '最新订单',
      viewAll: '查看全部',
      viewMoreOrders: '查看更多订单',
      orderStatus: {
        pending: '待付款',
        paid: '已付款',
        shipped: '已发货',
        completed: '已完成',
        cancelled: '已取消'
      },
      hotProducts: '热销商品',
      sales: '销量',
      viewMoreProducts: '查看更多商品',
      systemNotices: '系统通知',
      markAllRead: '全部已读',
      viewAllNotices: '查看全部通知',
      minutesAgo: '分钟前',
      hoursAgo: '小时前',
      daysAgo: '天前',
      sampleCustomer1: '张先生',
      sampleCustomer2: '李女士',
      sampleCustomer3: '王先生',
      sampleCustomer4: '赵女士',
      sampleCustomer5: '钱先生',
      sampleProduct1: 'iPhone 15 Pro Max 256GB',
      sampleProduct2: 'MacBook Pro 14英寸 M3芯片',
      sampleProduct3: 'AirPods Pro 第三代',
      sampleProduct4: 'iPad Air 第五代 256GB',
      sampleProduct5: 'Apple Watch Series 9',
      notice: {
        systemUpdate: '系统更新通知',
        systemUpdateContent: '系统将于今晚22:00进行版本更新，预计维护时间30分钟，请提前做好准备。',
        lowStock: '库存预警',
        lowStockContent: '有5件商品库存不足10件，请及时补货以免影响销售。',
        newOrder: '新订单提醒',
        newOrderContent: '您有3个新订单待处理，请及时查看并处理。',
        paymentFailed: '支付失败通知',
        paymentFailedContent: '订单FS202501010006支付失败，请联系客户重新支付。',
        promotion: '促销活动',
        promotionContent: '春节促销活动即将开始，请提前准备相关商品和营销素材。'
      },
      greeting: '早安，{userName}, 今天又是充满活力的一天!',
      weatherDesc: '今日多云转晴，20℃ - 25℃!',
      projectCount: '项目数',
      todo: '待办',
      message: '消息',
      downloadCount: '下载量',
      registerCount: '注册量',
      schedule: '作息安排',
      study: '学习',
      work: '工作',
      rest: '休息',
      entertainment: '娱乐',
      visitCount: '访问量',
      turnover: '成交额',
      dealCount: '成交量',
      projectNews: {
        title: '项目动态',
        moreNews: '更多动态',
        desc1: 'Soybean 在2021年5月28日创建了开源项目 soybean-admin!',
        desc2: 'Yanbowe 向 soybean-admin 提交了一个bug，多标签栏不会自适应。',
        desc3: 'Soybean 准备为 soybean-admin 的发布做充分的准备工作!',
        desc4: 'Soybean 正在忙于为soybean-admin写项目说明文档！',
        desc5: 'Soybean 刚才把工作台页面随便写了一些，凑合能看了！'
      },
      creativity: '创意'
    },
    userManagement: {
      title: '用户管理',
      description: '查看和管理所有系统用户信息',
      addUser: '新增用户',
      export: '导出数据',
      modules: {
        title: '功能模块'
      },
      overview: {
        title: '数据概览',
        totalUsers: '总用户数',
        activeUsers: '活跃用户',
        newUsers: '今日新增',
        vipUsers: 'VIP用户'
      },
      enterModule: '进入模块',
      users: {
        title: '用户管理',
        description: '查看和管理所有系统用户信息',
        addUser: '新增用户',
        editUser: '编辑用户',
        deleteUser: '删除用户',
        viewUser: '查看用户',
        exportData: '导出数据',
        search: '搜索',
        reset: '重置',
        searchPlaceholder: '请输入用户名、邮箱或手机号搜索',
        selectStatus: '选择状态',
        selectLevel: '选择等级',
        selectGroup: '选择分组',
        advancedFilter: '高级筛选',
        resetFilter: '重置筛选',
        filter: {
          userIdentity: '用户身份',
          paidMember: '付费会员',
          balanceRange: '储值余额',
          pointsRange: '积分剩余',
          lastConsumption: '上次消费',
          orderCount: '下单次数',
          totalSpent: '消费金额',
          rechargeCount: '充值次数',
          lastLogin: '访问情况',
          selectTags: '选择标签',
          selectIdentity: '选择身份',
          selectMemberStatus: '选择会员状态',
          selectBalanceRange: '选择余额范围',
          selectPointsRange: '选择积分范围',
          selectConsumptionTime: '选择消费时间',
          selectOrderRange: '选择订单范围',
          selectSpentRange: '选择消费范围',
          selectRechargeRange: '选择充值范围',
          selectLoginTime: '选择登录时间'
        },
        filterOptions: {
          identity: {
            normal: '普通用户',
            merchant: '商户',
            agent: '代理商',
            admin: '管理员'
          },
          memberStatus: {
            yes: '是',
            no: '否'
          },
          balanceRange: {
            range1: '0-100元',
            range2: '100-500元',
            range3: '500-1000元',
            range4: '1000-5000元',
            range5: '5000元以上'
          },
          pointsRange: {
            range1: '0-1000分',
            range2: '1000-5000分',
            range3: '5000-10000分',
            range4: '10000分以上'
          },
          consumptionTime: {
            days7: '7天内',
            days30: '30天内',
            days90: '90天内',
            days180: '180天内',
            never: '从未消费'
          },
          orderRange: {
            range1: '0-5单',
            range2: '5-20单',
            range3: '20-50单',
            range4: '50单以上'
          },
          spentRange: {
            range1: '0-500元',
            range2: '500-2000元',
            range3: '2000-5000元',
            range4: '5000-10000元',
            range5: '10000元以上'
          },
          rechargeRange: {
            range1: '0次',
            range2: '1-5次',
            range3: '5-20次',
            range4: '20次以上'
          },
          loginTime: {
            today: '今天',
            days7: '7天内',
            days30: '30天内',
            days90: '90天内',
            never: '从未登录'
          }
        },
        batchEnable: '批量启用',
        batchDisable: '批量禁用',
        batchDelete: '批量删除',
        selectedCount: '已选择 {count} 项',
        totalRecords: '共 {total} 条记录',
        showingRecords: '显示第 {start} - {end} 条，共 {total} 条记录',
        confirmDelete: '确认删除此用户？',
        confirmBatchDelete: '将删除 {count} 个用户，此操作不可恢复',
        deleteSuccess: '删除成功',
        batchEnableSuccess: '批量启用成功',
        batchDisableSuccess: '批量禁用成功',
        pleaseSelectUsers: '请先选择用户',
        exportingData: '正在导出数据...',
        columns: {
          avatar: '头像',
          username: '用户名',
          email: '邮箱',
          phone: '手机号',
          status: '状态',
          level: '等级',
          group: '分组',
          tags: '标签',
          orderCount: '订单数',
          totalSpent: '消费金额',
          registerTime: '注册时间',
          lastLoginTime: '最后登录',
          actions: '操作'
        },
        status: {
          active: '正常',
          disabled: '禁用',
          pending: '待验证'
        },
        statusActions: {
          enable: '启用',
          disable: '禁用'
        },
        actions: {
          view: '查看',
          edit: '编辑',
          delete: '删除',
          more: '更多操作',
          modifyBalance: '修改余额',
          modifyPoints: '修改积分',
          giftMembership: '赠送会员',
          setGroup: '设置分组',
          setTags: '设置标签',
          setPromoter: '修改上级推广人'
        },
        level: {
          normal: '普通用户',
          vip: 'VIP用户',
          svip: '超级VIP'
        },
        group: {
          normal: '普通用户',
          new: '新用户',
          vip: 'VIP用户'
        },
        form: {
          username: '用户名',
          email: '邮箱',
          phone: '手机号',
          password: '密码',
          confirmPassword: '确认密码',
          status: '状态',
          level: '等级',
          group: '分组',
          tags: '标签',
          avatar: '头像',
          usernamePlaceholder: '请输入用户名',
          emailPlaceholder: '请输入邮箱地址',
          phonePlaceholder: '请输入手机号',
          passwordPlaceholder: '请输入密码',
          confirmPasswordPlaceholder: '请再次输入密码',
          tagsPlaceholder: '请输入标签，按回车添加',
          usernameRequired: '请输入用户名',
          emailRequired: '请输入邮箱地址',
          emailInvalid: '请输入有效的邮箱地址',
          phoneRequired: '请输入手机号',
          phoneInvalid: '请输入有效的手机号',
          passwordRequired: '请输入密码',
          passwordMinLength: '密码长度不能少于6位',
          confirmPasswordRequired: '请确认密码',
          passwordNotMatch: '两次输入的密码不一致',
          uploadAvatar: '上传头像',
          avatarUploadTip: '点击或拖拽文件到此区域上传',
          avatarUploadDesc: '支持 JPG、PNG 格式，文件大小不超过 2MB',
          avatarUploadSuccess: '头像上传成功',
          avatarUploadError: '头像上传失败'
        },
        userDetail: {
          title: '用户详情',
          tabs: {
            userInfo: '用户信息',
            consumptionRecords: '消费记录',
            pointsDetail: '积分明细',
            signInRecords: '签到记录',
            coupons: '持有优惠券',
            balanceChanges: '余额变动',
            friendRelations: '好友关系'
          },
          overview: {
            title: '用户概览',
            balance: '余额',
            points: '积分',
            totalOrders: '总订单数',
            totalSpent: '总消费金额',
            monthlyOrders: '本月订单数',
            monthlySpent: '本月消费金额'
          },
          basicInfo: {
            title: '基本信息',
            userId: '用户ID',
            realName: '真实姓名',
            birthday: '生日',
            idCard: '身份证号',
            address: '用户地址'
          },
          accountInfo: {
            title: '账户信息',
            loginPassword: '登录密码'
          },
          userProfile: {
            title: '用户概况',
            promotionQualification: '推广资格',
            userStatus: '用户状态',
            userLevel: '用户等级',
            userTags: '用户标签',
            userGroup: '用户分组',
            promoter: '推广人',
            registerTime: '注册时间',
            lastLoginTime: '最后登录时间'
          },
          userNotes: {
            title: '用户备注',
            placeholder: '请输入用户备注信息',
            save: '保存备注'
          },
          consumptionRecords: {
            title: '消费记录',
            orderId: '订单ID',
            receiver: '收货人',
            quantity: '商品数量',
            actualAmount: '实付金额',
            completionTime: '交易完成时间'
          },
          pointsDetail: {
            title: '积分明细',
            source: '来源/用途',
            change: '积分变化',
            afterChange: '变化后积分',
            date: '日期',
            remark: '备注'
          },
          signInRecords: {
            title: '签到记录',
            signInDate: '签到日期',
            consecutiveDays: '连续签到天数',
            pointsEarned: '获得积分'
          },
          coupons: {
            title: '持有优惠券',
            name: '优惠券名称',
            type: '类型',
            amount: '面额',
            condition: '使用条件',
            validity: '有效期',
            status: '状态'
          },
          balanceChanges: {
            title: '余额变动',
            changeType: '变动类型',
            changeAmount: '变动金额',
            afterChange: '变动后余额',
            time: '时间',
            remark: '备注'
          },
          friendRelations: {
            title: '好友关系',
            friendUsername: '好友用户名',
            relationType: '关系类型',
            establishTime: '建立时间'
          },
          quickActions: {
            title: '快捷操作',
            modifyBalance: '修改余额',
            modifyPoints: '修改积分',
            giftCoupon: '赠送优惠券',
            giftMembership: '赠送会员',
            setTags: '设置标签',
            setGroup: '设置分组',
            setPromoter: '修改推广人',
            editNotes: '编辑备注'
          }
        }
      },
      statistics: {
        title: '用户统计',
        description: '查看用户增长趋势、来源分析和地域分布等统计数据',
        totalUsers: '总用户数',
        activeUsers: '活跃用户',
        newUsers: '新增用户',
        vipUsers: 'VIP用户',
        today: '今日',
        userGrowthTrend: '用户增长趋势',
        userSourceAnalysis: '用户来源分析',
        userSource: '用户来源',
        regionDistribution: '地域分布',
        timeRange: {
          '7d': '最近7天',
          '30d': '最近30天',
          '90d': '最近90天',
          '1y': '最近1年'
        },
        sources: {
          organic: '自然搜索',
          social: '社交媒体',
          direct: '直接访问',
          referral: '推荐链接',
          email: '邮件营销',
          ads: '广告投放'
        },
        regions: {
          beijing: '北京',
          shanghai: '上海',
          guangzhou: '广州',
          shenzhen: '深圳',
          hangzhou: '杭州',
          others: '其他地区'
        }
      },
      groups: {
        title: '用户分组',
        description: '创建和管理用户分组，配置分组权限',
        createGroup: '创建分组',
        editGroup: '编辑分组',
        search: '搜索',
        reset: '重置',
        advancedFilter: '高级筛选',
        exportData: '导出数据',
        searchPlaceholder: '搜索分组名称或描述',
        selectStatus: '选择状态',
        selectedCount: '已选择 {count} 项',
        totalCount: '共 {total} 条记录',
        showingRecords: '显示第 {start} - {end} 条，共 {total} 条记录',
        selectGroups: '请先选择分组',
        batchEnable: '批量启用',
        batchDisable: '批量禁用',
        batchEnableSuccess: '批量启用成功',
        batchDisableSuccess: '批量禁用成功',
        batchDelete: '批量删除',
        batchDeleteSuccess: '批量删除成功',
        confirmDelete: '确认删除此分组？',
        confirmBatchDelete: '确认删除选中的 {count} 个分组？',
        cannotDeleteWithUsers: '该分组下还有用户，无法删除',
        viewMembers: '查看成员',
        edit: '编辑',
        delete: '删除',
        enable: '启用',
        disable: '禁用',
        copy: '复制',
        clone: '克隆分组',
        cloneSuccess: '克隆分组成功',
        exportMembers: '导出成员',
        importMembers: '导入成员',
        statistics: '使用统计',
        quickSetPermissions: '快速设置权限',
        nameRequired: '分组名称不能为空',
        nameExists: '分组名称已存在',
        createSuccess: '创建分组成功',
        updateSuccess: '更新分组成功',
        deleteSuccess: '删除分组成功',
        enableSuccess: '启用成功',
        disableSuccess: '禁用成功',
        noPermissions: '无权限',
        noData: '暂无分组数据',
        totalGroups: '分组总数',
        exportDataSuccess: '导出分组数据成功',
        exportMembersSuccess: '导出成员数据成功',
        validation: {
          nameLength: '分组名称长度应在2-20个字符之间',
          descriptionLength: '描述长度不能超过100个字符'
        },
        status: {
          active: '启用',
          disabled: '禁用'
        },
        permissions: {
          browse: '浏览权限',
          purchase: '购买权限',
          comment: '评论权限',
          vipDiscount: 'VIP折扣',
          prioritySupport: '优先客服',
          exclusiveProducts: '专属商品',
          merchantManage: '商家管理',
          productPublish: '商品发布'
        },
        table: {
          title: '分组列表',
          name: '分组名称',
          description: '描述',
          userCount: '用户数量',
          permissions: '权限',
          status: '状态',
          updateTime: '更新时间',
          actions: '操作'
        },
        form: {
          name: '分组名称',
          namePlaceholder: '请输入分组名称',
          description: '分组描述',
          descriptionPlaceholder: '请输入分组描述',
          color: '分组颜色',
          permissions: '分组权限',
          save: '保存',
          cancel: '取消'
        },
        modal: {
          createTitle: '创建分组',
          editTitle: '编辑分组',
          cloneTitle: '克隆分组'
        },
        drawer: {
          membersTitle: '成员管理',
          statisticsTitle: '使用统计',
          memberCount: '成员数量',
          noMembers: '该分组暂无成员',
          addMember: '添加成员',
          removeMember: '移除成员',
          searchUser: '搜索并选择用户',
          addToGroup: '添加到分组'
        }
      },
      tags: {
        title: '用户标签',
        description: '创建和管理用户标签，为用户分类打标',
        createTag: '创建标签',
        editTag: '编辑标签',
        searchPlaceholder: '搜索标签名称或描述',
        selectCategory: '选择分类',
        selectStatus: '选择状态',
        selectedCount: '已选择 {count} 项',
        totalCount: '共 {total} 条记录',
        selectTags: '请先选择标签',
        batchEnable: '批量启用',
        batchDisable: '批量禁用',
        batchDelete: '批量删除',
        batchEnableSuccess: '批量启用成功',
        batchDisableSuccess: '批量禁用成功',
        confirmDelete: '确认删除此标签？',
        viewUsers: '查看用户',
        nameRequired: '标签名称不能为空',
        categoryRequired: '标签分类不能为空',
        createSuccess: '创建标签成功',
        updateSuccess: '更新标签成功',
        totalTags: '标签总数',
        users: '用户',
        tagList: '标签列表',
        categories: {
          userStatus: '用户状态',
          consumeBehavior: '消费行为',
          regionDistribution: '地域分布',
          deviceType: '设备类型',
          userType: '用户类型'
        },
        status: {
          active: '启用',
          disabled: '禁用'
        },
        table: {
          name: '标签名称',
          description: '描述',
          category: '分类',
          userCount: '用户数量',
          status: '状态',
          updateTime: '更新时间',
          actions: '操作'
        },
        form: {
          name: '标签名称',
          namePlaceholder: '请输入标签名称',
          description: '标签描述',
          descriptionPlaceholder: '请输入标签描述',
          category: '标签分类',
          categoryPlaceholder: '请选择标签分类',
          color: '标签颜色'
        }
      },
      levels: {
        title: '用户等级',
        description: '设置用户等级体系和升级规则',
        createLevel: '创建等级',
        editLevel: '编辑等级',
        viewUsers: '查看用户',
        confirmDelete: '确认删除此等级？',
        nameRequired: '等级名称不能为空',
        createSuccess: '创建等级成功',
        updateSuccess: '更新等级成功',
        totalLevels: '等级总数',
        users: '用户',
        levelSystem: '等级体系',
        levelList: '等级列表',
        maxLevel: '最高等级',
        pointsRequired: '所需积分',
        consumeRequired: '所需消费',
        timeRequired: '所需时间',
        days: '天',
        status: {
          active: '启用',
          disabled: '禁用'
        },
        table: {
          level: '等级',
          name: '等级名称',
          description: '描述',
          pointsRange: '积分范围',
          userCount: '用户数量',
          benefits: '权益',
          upgradeRules: '升级规则',
          status: '状态',
          actions: '操作'
        },
        form: {
          name: '等级名称',
          namePlaceholder: '请输入等级名称',
          description: '等级描述',
          descriptionPlaceholder: '请输入等级描述',
          level: '等级序号',
          icon: '等级图标',
          color: '等级颜色',
          minPoints: '最小积分',
          maxPoints: '最大积分',
          benefits: '等级权益',
          upgradeRules: '升级规则',
          pointsRequired: '所需积分',
          consumeRequired: '所需消费',
          timeRequired: '所需天数'
        }
      },
      settings: {
        title: '用户配置',
        description: '配置用户注册、权限和系统参数',
        registerSettings: '注册设置',
        permissionSettings: '权限配置',
        systemSettings: '系统参数',
        save: '保存设置',
        reset: '重置设置',
        saveSuccess: '保存成功',
        resetSuccess: '重置成功',
        allowRegister: '允许用户注册',
        requireEmailVerification: '邮箱验证',
        requirePhoneVerification: '手机验证',
        allowSocialLogin: '社交登录',
        defaultSettings: '默认设置',
        defaultUserGroup: '默认用户分组',
        defaultUserLevel: '默认用户等级',
        welcomeMessage: '欢迎消息',
        welcomeMessagePlaceholder: '请输入新用户注册后的欢迎消息',
        autoAssignTags: '自动分配标签',
        selectTags: '选择标签',
        registrationFields: '注册字段设置',
        required: '必填',
        minLength: '最小长度',
        maxLength: '最大长度',
        fields: {
          username: '用户名',
          email: '邮箱',
          phone: '手机号',
          realName: '真实姓名',
          birthday: '生日',
          gender: '性别',
          address: '地址'
        },
        defaultPermissions: '默认权限',
        guestPermissions: '游客权限',
        securitySettings: '安全设置',
        maxLoginAttempts: '最大登录尝试次数',
        lockoutDuration: '锁定时长',
        sessionTimeout: '会话超时',
        passwordChangeInterval: '密码更换周期',
        allowMultipleLogin: '允许多设备登录',
        requirePasswordChange: '强制密码更换',
        twoFactorAuth: '双因素认证',
        minutes: '分钟',
        hours: '小时',
        days: '天',
        permissions: {
          browse: '浏览权限',
          purchase: '购买权限',
          comment: '评论权限',
          review: '评价权限',
          share: '分享权限',
          invite: '邀请权限'
        }
      }
    }
  },
  form: {
    required: '不能为空',
    userName: {
      required: '请输入用户名',
      invalid: '用户名格式不正确'
    },
    phone: {
      required: '请输入手机号',
      invalid: '手机号格式不正确'
    },
    pwd: {
      required: '请输入密码',
      invalid: '密码格式不正确，6-18位字符，包含字母、数字、下划线'
    },
    confirmPwd: {
      required: '请输入确认密码',
      invalid: '两次输入密码不一致'
    },
    code: {
      required: '请输入验证码',
      invalid: '验证码格式不正确'
    },
    email: {
      required: '请输入邮箱',
      invalid: '邮箱格式不正确'
    }
  },
  dropdown: {
    closeCurrent: '关闭',
    closeOther: '关闭其它',
    closeLeft: '关闭左侧',
    closeRight: '关闭右侧',
    closeAll: '关闭所有'
  },
  icon: {
    themeConfig: '主题配置',
    themeSchema: '主题模式',
    lang: '切换语言',
    fullscreen: '全屏',
    fullscreenExit: '退出全屏',
    reload: '刷新页面',
    collapse: '折叠菜单',
    expand: '展开菜单',
    pin: '固定',
    unpin: '取消固定'
  },
  datatable: {
    itemCount: '共 {total} 条'
  }
};

export default local;
