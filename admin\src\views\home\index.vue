<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import EcommerceStats from './modules/ecommerce-stats.vue';
import SalesChart from './modules/sales-chart.vue';
import RecentOrders from './modules/recent-orders.vue';
import HotProducts from './modules/hot-products.vue';
import SystemNotices from './modules/system-notices.vue';

defineOptions({
  name: 'FutureShopHome'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 当前用户信息
const currentUser = ref({
  userName: 'Admin',
  role: 'admin',
  lastLoginTime: '2025-01-27 08:30:00'
});

// 今日统计数据
const todayStats = ref({
  orders: 156,
  sales: 89650,
  newUsers: 23
});

// 格式化日期时间
function formatDateTime(dateTime: string): string {
  const date = new Date(dateTime);
  return date.toLocaleString();
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 欢迎横幅 -->
      <NCard :bordered="false" class="card-wrapper">
        <div class="flex flex-col lg:flex-row gap-16px">
          <!-- 左侧欢迎信息 -->
          <div class="flex-1">
            <div class="flex items-center justify-between mb-16px">
              <div>
                <h1 class="text-24px font-bold text-primary mb-8px">
                  {{ $t('page.home.welcome') }} FutureShop {{ $t('page.home.dashboard') }}
                </h1>
                <div class="flex items-center space-x-16px text-14px text-gray-500">
                  <span>{{ $t('page.home.currentUser') }}: {{ currentUser.userName }}</span>
                  <span>{{ $t('page.home.userRole') }}: {{ $t(`page.home.roles.${currentUser.role}`) }}</span>
                  <span>{{ $t('page.home.lastLogin') }}: {{ formatDateTime(currentUser.lastLoginTime) }}</span>
                </div>
              </div>
              <SvgIcon icon="mdi:store" class="text-48px text-primary" />
            </div>

            <!-- 今日概览 -->
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-12px mb-16px">
              <div class="bg-blue-50 dark:bg-blue-900/20 p-12px rd-8px">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-12px text-gray-600 dark:text-gray-400">{{ $t('page.home.todayOrders') }}</p>
                    <p class="text-18px font-bold text-blue-600">{{ todayStats.orders }}</p>
                  </div>
                  <SvgIcon icon="mdi:shopping" class="text-20px text-blue-500" />
                </div>
              </div>
              <div class="bg-green-50 dark:bg-green-900/20 p-12px rd-8px">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-12px text-gray-600 dark:text-gray-400">{{ $t('page.home.todaySales') }}</p>
                    <p class="text-18px font-bold text-green-600">¥{{ todayStats.sales.toLocaleString() }}</p>
                  </div>
                  <SvgIcon icon="mdi:currency-cny" class="text-20px text-green-500" />
                </div>
              </div>
              <div class="bg-orange-50 dark:bg-orange-900/20 p-12px rd-8px">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-12px text-gray-600 dark:text-gray-400">{{ $t('page.home.todayUsers') }}</p>
                    <p class="text-18px font-bold text-orange-600">{{ todayStats.newUsers }}</p>
                  </div>
                  <SvgIcon icon="mdi:account-plus" class="text-20px text-orange-500" />
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧快捷操作 -->
          <div class="flex-shrink-0">
            <div class="flex flex-col space-y-8px">
              <h3 class="text-14px font-medium text-gray-700 dark:text-gray-300 mb-8px">
                {{ $t('page.home.quickActions') }}
              </h3>
              <NButton type="primary" size="small" class="justify-start">
                <template #icon>
                  <SvgIcon icon="mdi:plus" />
                </template>
                {{ $t('page.home.newOrder') }}
              </NButton>
              <NButton type="info" size="small" class="justify-start">
                <template #icon>
                  <SvgIcon icon="mdi:package-variant" />
                </template>
                {{ $t('page.home.productManagement') }}
              </NButton>
              <NButton type="success" size="small" class="justify-start">
                <template #icon>
                  <SvgIcon icon="mdi:chart-line" />
                </template>
                {{ $t('page.home.salesReport') }}
              </NButton>
            </div>
          </div>
        </div>
      </NCard>

      <!-- 数据统计卡片 -->
      <EcommerceStats />

      <!-- 图表和热销商品 -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <NGi span="24 s:24 m:16">
          <NCard :bordered="false" class="card-wrapper">
            <SalesChart />
          </NCard>
        </NGi>
        <NGi span="24 s:24 m:8">
          <HotProducts />
        </NGi>
      </NGrid>

      <!-- 最新订单和系统通知 -->
      <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
        <NGi span="24 s:24 m:16">
          <RecentOrders />
        </NGi>
        <NGi span="24 s:24 m:8">
          <SystemNotices />
        </NGi>
      </NGrid>
    </NSpace>
  </div>
</template>

<style scoped></style>
