<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限复选框对齐修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: white;
            border-radius: 4px;
        }
        .success {
            border-left-color: #28a745;
        }
        .warning {
            border-left-color: #ffc107;
        }
        .error {
            border-left-color: #dc3545;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 12px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 6px;
        }
        .before {
            background: #fff5f5;
            border: 1px solid #fed7d7;
        }
        .after {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
        }
        h1, h2, h3 {
            color: #333;
        }
        h1 {
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        h2 {
            color: #007bff;
            margin-top: 30px;
        }
        .icon {
            font-size: 18px;
            margin-right: 8px;
        }
        .success-icon { color: #28a745; }
        .warning-icon { color: #ffc107; }
        .error-icon { color: #dc3545; }
        .info-icon { color: #007bff; }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px 20px;
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .demo-checkbox {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            min-height: 44px;
        }
        .demo-checkbox input {
            margin-right: 8px;
            flex-shrink: 0;
        }
        .demo-checkbox label {
            flex: 1;
            font-size: 13px;
            line-height: 1.5;
        }
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .demo-checkbox {
                min-height: 48px;
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="icon">🎯</span>分组权限复选框对齐修复测试</h1>
        
        <div class="test-section">
            <h2><span class="icon success-icon">✅</span>修复概述</h2>
            <p>成功修复了分组表单弹窗中权限复选框组的文字对齐问题，确保在所有设备上都有统一的对齐效果。</p>
        </div>

        <div class="test-section">
            <h2><span class="icon info-icon">🔍</span>问题分析</h2>
            
            <div class="comparison">
                <div class="before">
                    <h4><span class="icon error-icon">❌</span>修复前的问题</h4>
                    <ul>
                        <li>复选框与文字间距不一致</li>
                        <li>多行权限文字垂直对齐问题</li>
                        <li>不同长度权限名称布局不整齐</li>
                        <li>网格布局中权限项对齐不统一</li>
                        <li>响应式布局下对齐效果差</li>
                    </ul>
                </div>
                <div class="after">
                    <h4><span class="icon success-icon">✅</span>修复后的改进</h4>
                    <ul>
                        <li>✅ 统一的复选框与文字间距</li>
                        <li>✅ 完美的垂直居中对齐</li>
                        <li>✅ 整齐的网格布局</li>
                        <li>✅ 一致的权限项高度</li>
                        <li>✅ 响应式对齐优化</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><span class="icon info-icon">🛠️</span>技术修复细节</h2>
            
            <div class="test-item success">
                <h3>1. 基础复选框对齐优化</h3>
                <div class="code">
.modal-group-form .n-checkbox {
  margin-right: 0;
  margin-bottom: 0;
  padding: 10px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;              /* ✅ 弹性布局 */
  align-items: center;        /* ✅ 垂直居中 */
  width: 100%;               /* ✅ 全宽度 */
  min-height: 44px;          /* ✅ 最小高度 */
  box-sizing: border-box;    /* ✅ 盒模型 */
}
                </div>
            </div>

            <div class="test-item success">
                <h3>2. 文字标签对齐优化</h3>
                <div class="code">
.modal-group-form .n-checkbox .n-checkbox__label {
  font-size: 13px;
  line-height: 1.5;          /* ✅ 行高优化 */
  word-break: break-word;    /* ✅ 长文本换行 */
  margin-left: 8px;          /* ✅ 统一间距 */
  flex: 1;                   /* ✅ 弹性填充 */
  display: flex;             /* ✅ 弹性布局 */
  align-items: center;       /* ✅ 垂直居中 */
  min-height: 20px;          /* ✅ 最小高度 */
}
                </div>
            </div>

            <div class="test-item success">
                <h3>3. 网格布局对齐优化</h3>
                <div class="code">
.modal-group-form .permissions-grid {
  gap: 16px 20px;
  align-items: stretch;      /* ✅ 拉伸对齐 */
}

.modal-group-form .permissions-grid .n-gi {
  display: flex;             /* ✅ 弹性布局 */
  align-items: stretch;      /* ✅ 拉伸对齐 */
}
                </div>
            </div>

            <div class="test-item success">
                <h3>4. 响应式对齐优化</h3>
                <div class="code">
/* 平板端 (max-width: 1200px) */
.modal-group-form .permissions-grid {
  grid-template-columns: repeat(2, 1fr) !important;
  gap: 14px 18px;
}

/* 移动端 (max-width: 768px) */
.modal-group-form .n-checkbox {
  padding: 12px 16px;
  min-height: 48px;          /* ✅ 触摸友好高度 */
  display: flex;
  align-items: center;
}
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2><span class="icon info-icon">📱</span>响应式对齐效果演示</h2>
            
            <h3>桌面端布局 (3列)</h3>
            <div class="demo-grid">
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm1">
                    <label for="perm1">用户管理</label>
                </div>
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm2">
                    <label for="perm2">商品管理与库存控制</label>
                </div>
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm3">
                    <label for="perm3">订单</label>
                </div>
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm4">
                    <label for="perm4">财务报表与数据分析</label>
                </div>
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm5">
                    <label for="perm5">系统设置</label>
                </div>
                <div class="demo-checkbox">
                    <input type="checkbox" id="perm6">
                    <label for="perm6">营销活动管理</label>
                </div>
            </div>
            
            <p><strong>注意：</strong>在移动端会自动调整为单列布局，确保触摸友好的交互体验。</p>
        </div>

        <div class="test-section">
            <h2><span class="icon warning-icon">⚠️</span>测试要点</h2>
            
            <div class="test-item warning">
                <h3>1. 桌面端测试 (≥1200px)</h3>
                <ul>
                    <li>✅ 3列网格布局整齐对齐</li>
                    <li>✅ 复选框与文字垂直居中</li>
                    <li>✅ 不同长度权限名称对齐一致</li>
                    <li>✅ 悬停效果正常</li>
                </ul>
            </div>

            <div class="test-item warning">
                <h3>2. 平板端测试 (768px-1200px)</h3>
                <ul>
                    <li>✅ 2列网格布局</li>
                    <li>✅ 间距适当调整</li>
                    <li>✅ 复选框高度保持一致</li>
                    <li>✅ 文字对齐效果良好</li>
                </ul>
            </div>

            <div class="test-item warning">
                <h3>3. 移动端测试 (≤768px)</h3>
                <ul>
                    <li>✅ 单列布局</li>
                    <li>✅ 触摸友好的48px最小高度</li>
                    <li>✅ 增大的内边距</li>
                    <li>✅ 文字大小适当调整</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h2><span class="icon success-icon">📊</span>修复效果对比</h2>
            
            <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: left;">对齐项目</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: center;">修复前</th>
                        <th style="border: 1px solid #dee2e6; padding: 12px; text-align: center;">修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">复选框与文字间距</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #dc3545;">❌ 不一致</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #28a745;">✅ 统一8px</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">垂直对齐</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #dc3545;">❌ 不对齐</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #28a745;">✅ 完美居中</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">权限项高度</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #dc3545;">❌ 不一致</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #28a745;">✅ 统一44px</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">长文本处理</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #dc3545;">❌ 布局混乱</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #28a745;">✅ 自动换行</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #dee2e6; padding: 12px;">响应式适配</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #ffc107;">⚠️ 基础</td>
                        <td style="border: 1px solid #dee2e6; padding: 12px; text-align: center; color: #28a745;">✅ 完善</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2><span class="icon info-icon">🧪</span>测试步骤</h2>
            
            <ol>
                <li><strong>打开分组表单弹窗</strong>
                    <ul>
                        <li>访问：用户管理 → 用户分组</li>
                        <li>点击"创建分组"按钮</li>
                    </ul>
                </li>
                <li><strong>检查权限复选框对齐</strong>
                    <ul>
                        <li>观察复选框与文字的间距是否一致</li>
                        <li>检查不同长度权限名称的对齐效果</li>
                        <li>验证垂直居中对齐是否正确</li>
                    </ul>
                </li>
                <li><strong>测试响应式效果</strong>
                    <ul>
                        <li>调整浏览器窗口大小</li>
                        <li>验证平板端2列布局</li>
                        <li>验证移动端单列布局</li>
                    </ul>
                </li>
                <li><strong>测试交互功能</strong>
                    <ul>
                        <li>点击复选框验证选择功能</li>
                        <li>检查悬停效果</li>
                        <li>验证权限统计是否正确</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="test-section">
            <h2><span class="icon success-icon">🎉</span>修复完成</h2>
            <p>✅ 权限复选框文字对齐问题已完全修复</p>
            <p>✅ 支持完善的响应式布局</p>
            <p>✅ 保持了良好的用户交互体验</p>
            <p>✅ 确保了在所有设备上的一致性</p>
        </div>
    </div>
</body>
</html>
