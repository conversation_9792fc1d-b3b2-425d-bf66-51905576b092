<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'SystemNotices'
});

interface NoticeItem {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'success' | 'error';
  createTime: string;
  isRead: boolean;
}

const systemNotices = computed<NoticeItem[]>(() => [
  {
    id: '1',
    title: $t('page.home.notice.systemUpdate'),
    content: $t('page.home.notice.systemUpdateContent'),
    type: 'info',
    createTime: '2025-01-27 09:00:00',
    isRead: false
  },
  {
    id: '2',
    title: $t('page.home.notice.lowStock'),
    content: $t('page.home.notice.lowStockContent'),
    type: 'warning',
    createTime: '2025-01-27 08:30:00',
    isRead: false
  },
  {
    id: '3',
    title: $t('page.home.notice.newOrder'),
    content: $t('page.home.notice.newOrderContent'),
    type: 'success',
    createTime: '2025-01-27 08:00:00',
    isRead: true
  },
  {
    id: '4',
    title: $t('page.home.notice.paymentFailed'),
    content: $t('page.home.notice.paymentFailedContent'),
    type: 'error',
    createTime: '2025-01-26 18:45:00',
    isRead: true
  },
  {
    id: '5',
    title: $t('page.home.notice.promotion'),
    content: $t('page.home.notice.promotionContent'),
    type: 'info',
    createTime: '2025-01-26 16:20:00',
    isRead: true
  }
]);

const noticeTypeConfig = computed(() => ({
  info: {
    icon: 'mdi:information',
    color: 'text-blue-500'
  },
  warning: {
    icon: 'mdi:alert',
    color: 'text-orange-500'
  },
  success: {
    icon: 'mdi:check-circle',
    color: 'text-green-500'
  },
  error: {
    icon: 'mdi:alert-circle',
    color: 'text-red-500'
  }
}));

function getNoticeConfig(type: NoticeItem['type']) {
  return noticeTypeConfig.value[type];
}

function formatTime(time: string): string {
  const date = new Date(time);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60));
    return `${minutes}${$t('page.home.minutesAgo')}`;
  } else if (hours < 24) {
    return `${hours}${$t('page.home.hoursAgo')}`;
  } else {
    const days = Math.floor(hours / 24);
    return `${days}${$t('page.home.daysAgo')}`;
  }
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <SvgIcon icon="mdi:bell" class="text-20px mr-8px text-primary" />
          <span class="text-16px font-medium">{{ $t('page.home.systemNotices') }}</span>
          <NBadge
            :value="systemNotices.filter(n => !n.isRead).length"
            :max="99"
            class="ml-8px"
            :show="systemNotices.filter(n => !n.isRead).length > 0"
          />
        </div>
        <NButton text type="primary" size="small">
          {{ $t('page.home.markAllRead') }}
        </NButton>
      </div>
    </template>

    <div class="space-y-8px max-h-400px overflow-y-auto">
      <div 
        v-for="notice in systemNotices" 
        :key="notice.id"
        class="flex items-start space-x-12px p-12px hover:bg-gray-50 dark:hover:bg-gray-800 rd-8px transition-colors"
        :class="{ 'opacity-60': notice.isRead }"
      >
        <!-- 通知图标 -->
        <div class="flex-shrink-0 mt-2px">
          <SvgIcon 
            :icon="getNoticeConfig(notice.type).icon" 
            class="text-16px"
            :class="getNoticeConfig(notice.type).color"
          />
        </div>

        <!-- 通知内容 -->
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between mb-4px">
            <h4 class="text-14px font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ notice.title }}
            </h4>
            <div class="flex items-center space-x-4px flex-shrink-0">
              <span class="text-12px text-gray-500">
                {{ formatTime(notice.createTime) }}
              </span>
              <div 
                v-if="!notice.isRead"
                class="w-6px h-6px bg-red-500 rd-full"
              ></div>
            </div>
          </div>
          <p class="text-12px text-gray-600 dark:text-gray-400 line-clamp-2">
            {{ notice.content }}
          </p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <NButton type="primary" ghost size="small">
          {{ $t('page.home.viewAllNotices') }}
        </NButton>
      </div>
    </template>
  </NCard>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
