<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'HotProducts'
});

interface ProductItem {
  id: string;
  name: string;
  image: string;
  price: number;
  sales: number;
  rank: number;
}

const hotProducts = computed<ProductItem[]>(() => [
  {
    id: '1',
    name: $t('page.home.sampleProduct1'),
    image: '/images/products/iphone15.jpg',
    price: 9999.00,
    sales: 1234,
    rank: 1
  },
  {
    id: '2',
    name: $t('page.home.sampleProduct2'),
    image: '/images/products/macbook.jpg',
    price: 15999.00,
    sales: 856,
    rank: 2
  },
  {
    id: '3',
    name: $t('page.home.sampleProduct3'),
    image: '/images/products/airpods.jpg',
    price: 1999.00,
    sales: 2341,
    rank: 3
  },
  {
    id: '4',
    name: $t('page.home.sampleProduct4'),
    image: '/images/products/ipad.jpg',
    price: 5199.00,
    sales: 567,
    rank: 4
  },
  {
    id: '5',
    name: $t('page.home.sampleProduct5'),
    image: '/images/products/watch.jpg',
    price: 3199.00,
    sales: 789,
    rank: 5
  }
]);

function getRankColor(rank: number): string {
  switch (rank) {
    case 1:
      return 'text-yellow-500';
    case 2:
      return 'text-gray-400';
    case 3:
      return 'text-orange-500';
    default:
      return 'text-gray-600';
  }
}

function getRankIcon(rank: number): string {
  if (rank <= 3) {
    return 'mdi:medal';
  }
  return 'mdi:numeric-' + rank + '-circle';
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <template #header>
      <div class="flex items-center">
        <SvgIcon icon="mdi:fire" class="text-20px mr-8px text-red-500" />
        <span class="text-16px font-medium">{{ $t('page.home.hotProducts') }}</span>
      </div>
    </template>

    <div class="space-y-12px">
      <div 
        v-for="product in hotProducts" 
        :key="product.id"
        class="flex items-center space-x-12px p-8px hover:bg-gray-50 dark:hover:bg-gray-800 rd-8px transition-colors"
      >
        <!-- 排名 -->
        <div class="flex-shrink-0 w-24px text-center">
          <SvgIcon 
            :icon="getRankIcon(product.rank)" 
            class="text-20px"
            :class="getRankColor(product.rank)"
          />
        </div>

        <!-- 商品图片 -->
        <div class="flex-shrink-0">
          <div class="w-40px h-40px bg-gray-200 dark:bg-gray-700 rd-6px flex items-center justify-center">
            <SvgIcon icon="mdi:package-variant" class="text-20px text-gray-400" />
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="flex-1 min-w-0">
          <div class="text-14px font-medium text-gray-900 dark:text-gray-100 truncate mb-2px">
            {{ product.name }}
          </div>
          <div class="flex items-center justify-between">
            <span class="text-12px text-primary font-bold">
              ¥{{ product.price.toFixed(2) }}
            </span>
            <span class="text-12px text-gray-500">
              {{ $t('page.home.sales') }}: {{ product.sales }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <NButton type="primary" ghost size="small">
          {{ $t('page.home.viewMoreProducts') }}
        </NButton>
      </div>
    </template>
  </NCard>
</template>

<style scoped></style>
