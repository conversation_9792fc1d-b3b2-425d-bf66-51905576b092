/*
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
*/

/* 弹窗尺寸样式 */

/* 小尺寸弹窗 - 适用于确认对话框、简单表单 */
.modal-small {
  width: 500px;
  max-width: 95vw;
}

/* 中等尺寸弹窗 - 适用于一般表单 */
.modal-medium {
  width: 700px;
  max-width: 90vw;
}

/* 大尺寸弹窗 - 适用于复杂表单、详情查看 */
.modal-large {
  width: 900px;
  max-width: 90vw;
}

/* 超大尺寸弹窗 - 适用于超复杂表单、数据导入导出 */
.modal-extra-large {
  width: 1200px;
  max-width: 95vw;
}

/* 全屏弹窗 */
.modal-full {
  width: 95vw;
  max-width: 95vw;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-small,
  .modal-medium,
  .modal-large,
  .modal-extra-large {
    width: 95vw !important;
    max-width: 95vw !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .modal-large {
    width: 85vw;
  }
  
  .modal-extra-large {
    width: 90vw;
  }
}

/* 弹窗内容优化 */
.n-modal .n-dialog {
  margin: 16px;
}

.n-modal .n-dialog__content {
  padding: 24px;
}

.n-modal .n-dialog__action {
  padding: 16px 24px;
  border-top: 1px solid var(--n-divider-color);
  margin-top: 16px;
}

/* 表单内弹窗优化 */
.modal-form .n-form {
  margin-bottom: 0;
}

.modal-form .n-form-item {
  margin-bottom: 18px;
}

.modal-form .n-form-item:last-child {
  margin-bottom: 0;
}

/* 抽屉样式优化 */
.n-drawer .n-drawer-content {
  padding: 0;
}

.n-drawer .n-drawer-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--n-divider-color);
}

.n-drawer .n-drawer-body {
  padding: 24px;
}

/* 弹窗动画优化 */
.n-modal {
  --n-bezier: cubic-bezier(0.4, 0, 0.2, 1);
}

.n-modal-container {
  transition: all 0.3s var(--n-bezier);
}

/* 暗色主题适配 */
[data-theme="dark"] .n-modal .n-dialog__action {
  border-top-color: var(--n-divider-color);
}

[data-theme="dark"] .n-drawer .n-drawer-header {
  border-bottom-color: var(--n-divider-color);
}

/* 移动端优化 */
@media (max-width: 640px) {
  .n-modal .n-dialog {
    margin: 8px;
  }
  
  .n-modal .n-dialog__content {
    padding: 16px;
  }
  
  .n-modal .n-dialog__action {
    padding: 12px 16px;
  }
  
  .n-drawer .n-drawer-body {
    padding: 16px;
  }
}

/* 特殊内容类型的弹窗样式 */

/* 图片预览弹窗 */
.modal-image-preview {
  width: 80vw;
  max-width: 1000px;
  height: 80vh;
}

.modal-image-preview .n-dialog__content {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数据表格弹窗 */
.modal-data-table {
  width: 90vw;
  max-width: 1400px;
  height: 80vh;
}

.modal-data-table .n-dialog__content {
  padding: 16px;
  height: calc(80vh - 120px);
  overflow: hidden;
}

/* 详情查看弹窗 */
.modal-detail-view {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;
}

.modal-detail-view .n-dialog__content {
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

/* 确认对话框优化 */
.modal-confirm {
  width: 400px;
  max-width: 90vw;
}

.modal-confirm .n-dialog__content {
  padding: 24px;
  text-align: center;
}

.modal-confirm .n-dialog__action {
  justify-content: center;
  gap: 12px;
}

/* 表单验证错误时的样式 */
.modal-form .n-form-item--feedback-error {
  margin-bottom: 24px;
}

/* 加载状态样式 */
.modal-loading .n-dialog__content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 空状态样式 */
.modal-empty .n-dialog__content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  flex-direction: column;
}

/* 滚动条样式优化 */
.n-modal .n-dialog__content::-webkit-scrollbar {
  width: 6px;
}

.n-modal .n-dialog__content::-webkit-scrollbar-track {
  background: transparent;
}

.n-modal .n-dialog__content::-webkit-scrollbar-thumb {
  background: var(--n-scrollbar-color);
  border-radius: 3px;
}

.n-modal .n-dialog__content::-webkit-scrollbar-thumb:hover {
  background: var(--n-scrollbar-color-hover);
}
