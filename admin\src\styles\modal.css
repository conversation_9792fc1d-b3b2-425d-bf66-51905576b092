/*
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
*/

/* 弹窗尺寸样式 */

/* 小尺寸弹窗 - 适用于确认对话框、简单表单 */
.modal-small {
  width: 500px;
  max-width: 95vw;
}

/* 中等尺寸弹窗 - 适用于一般表单 */
.modal-medium {
  width: 900px;
  max-width: 90vw;
  min-height: 400px;
  max-height: 85vh;
}

/* 大尺寸弹窗 - 适用于复杂表单、详情查看 */
.modal-large {
  width: 1100px;
  max-width: 90vw;
  min-height: 500px;
  max-height: 85vh;
}

/* 超大尺寸弹窗 - 适用于超复杂表单、数据导入导出 */
.modal-extra-large {
  width: 1300px;
  max-width: 95vw;
  min-height: 600px;
  max-height: 90vh;
}

/* 全屏弹窗 - 适用于特别复杂的表单 */
.modal-fullscreen {
  width: 95vw;
  height: 95vh;
  max-width: none;
  max-height: none;
}

/* 自适应高度弹窗 - 内容自动调整 */
.modal-auto-height {
  max-height: 90vh;
  height: auto;
}

/* 全屏弹窗 */
.modal-full {
  width: 95vw;
  max-width: 95vw;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-small,
  .modal-medium,
  .modal-large,
  .modal-extra-large {
    width: 95vw !important;
    max-width: 95vw !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .modal-medium {
    width: 80vw;
  }

  .modal-large {
    width: 85vw;
  }

  .modal-extra-large {
    width: 90vw;
  }
}

@media (min-width: 1025px) and (max-width: 1200px) {
  .modal-extra-large {
    width: 95vw;
  }
}

/* 弹窗内容优化 */
.n-modal .n-dialog {
  margin: 16px;
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 32px);
  min-width: 600px; /* 确保最小宽度 */
}

.n-modal .n-dialog__content {
  padding: 32px;
  min-height: 300px;
  overflow-y: auto;
  flex: 1;
}

.n-modal .n-dialog__action {
  padding: 20px 32px;
  border-top: 1px solid var(--n-divider-color);
  margin-top: 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0;
  background: var(--n-modal-color);
}

/* 表单内弹窗优化 */
.modal-form .n-form {
  margin-bottom: 0;
}

.modal-form .n-form-item {
  margin-bottom: 24px;
}

.modal-form .n-form-item:last-child {
  margin-bottom: 0;
}

.modal-form .n-form-item .n-form-item-label {
  padding-bottom: 8px;
  font-weight: 500;
}

.modal-form .n-input,
.modal-form .n-select,
.modal-form .n-color-picker {
  min-height: 40px;
}

.modal-form .n-input--textarea .n-input__input-el {
  min-height: 100px;
}

/* 弹窗标题样式优化 */
.n-modal .n-dialog__title {
  font-size: 18px;
  font-weight: 600;
  padding: 24px 32px 16px;
}

/* 弹窗关闭按钮优化 */
.n-modal .n-dialog__close {
  top: 20px;
  right: 20px;
}

/* 表单标签样式优化 */
.modal-form .n-form-item-label__text {
  color: var(--n-label-text-color);
  font-weight: 500;
}

/* 复选框组样式优化 */
.modal-form .n-checkbox-group {
  gap: 16px;
}

.modal-form .n-checkbox {
  margin-right: 0;
}

/* 颜色选择器样式优化 */
.modal-form .n-color-picker {
  width: 100%;
}

/* 按钮样式优化 */
.n-modal .n-dialog__action .n-button {
  min-width: 80px;
  height: 36px;
}

.n-modal .n-dialog__action .n-button--primary {
  font-weight: 500;
}

/* 复杂表单弹窗优化 */
.modal-form .n-divider {
  margin: 24px 0;
}

.modal-form .n-divider .n-divider__title {
  font-size: 16px;
  font-weight: 600;
}

.modal-form .n-checkbox-group {
  width: 100%;
}

.modal-form .n-checkbox {
  margin-right: 0;
  margin-bottom: 8px;
}

.modal-form .n-input-number {
  width: 100%;
}

/* 等级弹窗特殊优化 */
.modal-auto-height .n-dialog__content {
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

/* 权益复选框组优化 */
.modal-form .n-checkbox-group .n-grid {
  width: 100%;
}

.modal-form .n-checkbox-group .n-grid .n-gi {
  display: flex;
  align-items: center;
}

/* 确保表单字段有足够宽度 */
.modal-form .n-grid .n-gi {
  min-width: 200px;
}

.modal-form .n-input,
.modal-form .n-select,
.modal-form .n-input-number,
.modal-form .n-color-picker {
  width: 100%;
  min-width: 180px;
}

/* 文本域特殊处理 */
.modal-form .n-input--textarea {
  min-width: 300px;
}

/* 用户表单弹窗专用优化 */
.modal-user-form {
  width: 1400px !important;
  max-width: 95vw !important;
  min-height: 700px;
  max-height: 90vh;
}

.modal-user-form .n-dialog__content {
  padding: 40px;
  min-height: 600px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.modal-user-form .n-form {
  margin-bottom: 0;
}

.modal-user-form .n-form-item {
  margin-bottom: 28px;
}

.modal-user-form .n-form-item-label {
  min-width: 160px;
  padding-right: 16px;
  font-weight: 500;
  color: var(--n-label-text-color);
}

.modal-user-form .n-grid {
  gap: 28px 32px;
}

.modal-user-form .n-input,
.modal-user-form .n-select {
  min-height: 42px;
  font-size: 14px;
}

.modal-user-form .n-dynamic-tags {
  min-height: 42px;
}

/* 头像上传区域优化 */
.modal-user-form .avatar-upload-section {
  padding: 20px;
  border: 1px dashed var(--n-border-color);
  border-radius: 8px;
  background: var(--n-color-target);
}

.modal-user-form .avatar-upload-section .n-avatar {
  border: 2px solid var(--n-border-color);
}

.modal-user-form .avatar-upload-desc {
  margin-top: 12px;
  font-size: 12px;
  color: var(--n-text-color-disabled);
  line-height: 1.5;
}

/* 用户表单弹窗响应式优化 */
@media (max-width: 1200px) {
  .modal-user-form {
    width: 95vw !important;
  }

  .modal-user-form .n-form-item-label {
    min-width: 140px;
  }
}

@media (max-width: 768px) {
  .modal-user-form {
    width: 98vw !important;
    min-height: 500px;
  }

  .modal-user-form .n-dialog__content {
    padding: 24px;
    max-height: calc(100vh - 120px);
  }

  .modal-user-form .n-grid {
    grid-template-columns: 1fr !important;
    gap: 20px;
  }

  .modal-user-form .n-form-item-label {
    min-width: 120px;
    padding-right: 12px;
  }

  .modal-user-form .avatar-upload-section {
    padding: 16px;
  }

  .modal-user-form .avatar-upload-section .flex {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .modal-user-form .n-dialog__content {
    padding: 16px;
  }

  .modal-user-form .n-form-item-label {
    min-width: 100px;
    font-size: 13px;
  }

  .modal-user-form .n-input,
  .modal-user-form .n-select {
    min-height: 44px;
  }
}

/* 用户表单验证样式优化 */
.modal-user-form .n-form-item-feedback-wrapper {
  min-height: 24px;
  margin-top: 4px;
}

.modal-user-form .n-form-item--validation-error .n-form-item-label {
  color: var(--n-color-error);
}

.modal-user-form .n-form-item-feedback {
  font-size: 12px;
  line-height: 1.4;
}

/* 用户表单按钮区域优化 */
.modal-user-form .n-dialog__action {
  padding: 24px 40px;
  border-top: 2px solid var(--n-divider-color);
  background: var(--n-modal-color);
}

.modal-user-form .n-dialog__action .n-button {
  min-width: 120px;
  height: 40px;
  font-weight: 500;
}

/* 用户表单加载状态优化 */
.modal-user-form .n-button--loading {
  pointer-events: none;
}

.modal-user-form .n-upload {
  width: 100%;
}

.modal-user-form .n-upload .n-button {
  width: 100%;
  justify-content: center;
}

/* 分组表单弹窗专用优化 */
.modal-group-form {
  width: 1200px !important;
  max-width: 95vw !important;
  min-height: 600px;
  max-height: 85vh;
}

.modal-group-form .n-dialog__content {
  padding: 36px;
  min-height: 500px;
  max-height: calc(85vh - 140px);
  overflow-y: auto;
}

.modal-group-form .n-form {
  margin-bottom: 0;
}

.modal-group-form .n-form-item {
  margin-bottom: 26px;
}

.modal-group-form .n-form-item-label {
  min-width: 140px;
  padding-right: 16px;
  font-weight: 500;
  color: var(--n-label-text-color);
}

.modal-group-form .n-grid {
  gap: 26px 30px;
}

.modal-group-form .n-input,
.modal-group-form .n-select,
.modal-group-form .n-color-picker {
  min-height: 40px;
  font-size: 14px;
}

.modal-group-form .n-input--textarea {
  min-height: 100px;
}

/* ==================== 分组权限模块样式 ==================== */

/* 权限模块主容器 */
.modal-group-form .group-permissions-module {
  width: 100%;
}

/* 权限描述文字 */
.modal-group-form .permissions-description {
  margin-bottom: 16px;
  padding: 0;
  font-size: 13px;
  line-height: 1.6;
  color: var(--n-text-color-disabled);
  background: transparent;
  border: none;
}

/* 权限选择区域 */
.modal-group-form .permissions-selection-area {
  margin-bottom: 20px;
  padding: 24px;
  background: var(--n-color-target);
  border: 1px solid var(--n-border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.modal-group-form .permissions-selection-area:hover {
  border-color: var(--n-color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 权限网格布局 */
.modal-group-form .permissions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 20px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .modal-group-form .permissions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .modal-group-form .permissions-grid {
    grid-template-columns: 1fr;
  }
}

/* 权限项样式 */
.modal-group-form .permission-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: var(--n-color-hover);
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-height: 48px;
}

.modal-group-form .permission-item:hover {
  background: var(--n-color-pressed);
  border-color: var(--n-color-primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 权限统计和预览区域 */
.modal-group-form .permissions-summary {
  padding: 16px 20px;
  background: var(--n-color-hover);
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
}

/* 权限统计信息 */
.modal-group-form .permissions-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.modal-group-form .stats-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color);
}

.modal-group-form .stats-hint {
  font-size: 12px;
  color: var(--n-text-color-disabled);
}

/* 权限预览区域 */
.modal-group-form .permissions-preview {
  margin-top: 12px;
}

.modal-group-form .preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .modal-group-form .permissions-selection-area {
    padding: 16px;
  }

  .modal-group-form .permission-item {
    padding: 10px 12px;
    min-height: 44px;
  }

  .modal-group-form .permissions-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

.modal-group-form .permissions-grid {
  gap: 16px 20px;
  align-items: stretch;
}

.modal-group-form .permissions-grid .n-gi {
  display: flex;
  align-items: stretch;
}

.modal-group-form .n-checkbox {
  margin-right: 0;
  margin-bottom: 0;
  padding: 10px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 44px;
  box-sizing: border-box;
}

.modal-group-form .n-checkbox:hover {
  background: var(--n-color-hover);
}

.modal-group-form .n-checkbox--checked {
  background: var(--n-color-primary-suppl);
}

.modal-group-form .n-color-picker {
  width: 100%;
}

/* 分组表单弹窗响应式优化 */
@media (max-width: 1200px) {
  .modal-group-form {
    width: 95vw !important;
  }

  .modal-group-form .n-form-item-label {
    min-width: 120px;
  }

  .modal-group-form .permissions-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 14px 18px;
  }

  .modal-group-form .permissions-grid .n-gi {
    display: flex;
    align-items: stretch;
    min-height: 42px;
  }

  .modal-group-form .n-checkbox {
    min-height: 42px;
    padding: 10px 14px;
  }
}

@media (max-width: 768px) {
  .modal-group-form {
    width: 98vw !important;
    min-height: 500px;
  }

  .modal-group-form .n-dialog__content {
    padding: 24px;
    max-height: calc(100vh - 120px);
  }

  .modal-group-form .n-grid {
    grid-template-columns: 1fr !important;
    gap: 20px;
  }

  .modal-group-form .n-form-item-label {
    min-width: 100px;
    padding-right: 12px;
  }

  .modal-group-form .permissions-section {
    padding: 16px;
  }

  .modal-group-form .permissions-grid {
    grid-template-columns: 1fr !important;
    gap: 12px;
  }

  .modal-group-form .n-checkbox {
    padding: 12px 16px;
    width: 100%;
    justify-content: flex-start;
    min-height: 48px;
    display: flex;
    align-items: center;
  }

  .modal-group-form .n-checkbox .n-checkbox__label {
    font-size: 14px;
    line-height: 1.5;
    margin-left: 10px;
  }
}

@media (max-width: 480px) {
  .modal-group-form .n-dialog__content {
    padding: 16px;
  }

  .modal-group-form .n-form-item-label {
    min-width: 80px;
    font-size: 13px;
  }

  .modal-group-form .n-input,
  .modal-group-form .n-select,
  .modal-group-form .n-color-picker {
    min-height: 44px;
  }

  .modal-group-form .permissions-section {
    padding: 12px;
  }
}

/* 分组表单验证样式优化 */
.modal-group-form .n-form-item-feedback-wrapper {
  min-height: 24px;
  margin-top: 4px;
}

.modal-group-form .n-form-item--validation-error .n-form-item-label {
  color: var(--n-color-error);
}

.modal-group-form .n-form-item-feedback {
  font-size: 12px;
  line-height: 1.4;
}

/* 分组表单按钮区域优化 */
.modal-group-form .n-dialog__action {
  padding: 24px 36px;
  border-top: 2px solid var(--n-divider-color);
  background: var(--n-modal-color);
}

.modal-group-form .n-dialog__action .n-button {
  min-width: 120px;
  height: 40px;
  font-weight: 500;
}

/* 分组表单特殊组件优化 */
.modal-group-form .n-input--textarea .n-input__textarea-el {
  resize: vertical;
  min-height: 100px;
}

.modal-group-form .n-color-picker .n-color-picker-trigger {
  width: 100%;
  min-height: 40px;
}

/* 权限复选框组特殊样式 */
.modal-group-form .permissions-section .n-text {
  display: block;
  margin-bottom: 8px;
}

.modal-group-form .n-checkbox-group {
  width: 100%;
}

.modal-group-form .n-checkbox .n-checkbox__label {
  font-size: 13px;
  line-height: 1.5;
  word-break: break-word;
  margin-left: 8px;
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 20px;
}

/* 复选框图标对齐优化 */
.modal-group-form .n-checkbox .n-checkbox-box {
  flex-shrink: 0;
  margin-right: 0;
}

.modal-group-form .n-checkbox .n-checkbox-box__border {
  border-radius: 4px;
}

/* 复选框文字容器对齐 */
.modal-group-form .n-checkbox .n-checkbox__label-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 20px;
}

/* 权限复选框组整体对齐优化 */
.modal-group-form .n-checkbox-group {
  width: 100%;
}

.modal-group-form .n-checkbox-group .n-grid {
  width: 100%;
}

.modal-group-form .n-checkbox-group .n-grid .n-gi {
  display: flex;
  align-items: stretch;
  min-height: 44px;
}

/* 确保复选框在网格中的对齐 */
.modal-group-form .permissions-grid .n-checkbox {
  height: 100%;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
}

/* 长文本权限名称的处理 */
.modal-group-form .n-checkbox .n-checkbox__label {
  white-space: normal;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* 权限统计信息样式 */
.modal-group-form .permissions-section .border-t {
  border-top: 1px solid var(--n-divider-color);
}

.modal-group-form .permissions-section .text-gray-500 {
  color: var(--n-text-color-disabled);
}

.modal-group-form .permissions-section .text-gray-700 {
  color: var(--n-text-color);
}

/* 抽屉样式优化 */
.n-drawer .n-drawer-content {
  padding: 0;
}

.n-drawer .n-drawer-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--n-divider-color);
}

.n-drawer .n-drawer-body {
  padding: 24px;
}

/* 弹窗动画优化 */
.n-modal {
  --n-bezier: cubic-bezier(0.4, 0, 0.2, 1);
}

.n-modal-container {
  transition: all 0.3s var(--n-bezier);
}

/* 暗色主题适配 */
[data-theme="dark"] .n-modal .n-dialog__action {
  border-top-color: var(--n-divider-color);
}

[data-theme="dark"] .n-drawer .n-drawer-header {
  border-bottom-color: var(--n-divider-color);
}

/* 移动端优化 */
@media (max-width: 640px) {
  .n-modal .n-dialog {
    margin: 8px;
    max-height: calc(100vh - 16px);
    min-width: 95vw; /* 移动端使用视口宽度 */
  }

  .n-modal .n-dialog__content {
    padding: 20px;
    min-height: 250px;
    max-height: calc(100vh - 120px);
  }

  .n-modal .n-dialog__action {
    padding: 16px 20px;
  }

  .n-drawer .n-drawer-body {
    padding: 16px;
  }

  .modal-form .n-form-item {
    margin-bottom: 20px;
  }

  .modal-form .n-input,
  .modal-form .n-select,
  .modal-form .n-color-picker,
  .modal-form .n-input-number {
    min-height: 44px;
  }

  /* 移动端复杂弹窗优化 */
  .modal-extra-large,
  .modal-large {
    width: 95vw;
    max-width: none;
  }

  .modal-form .n-checkbox-group .n-grid {
    grid-template-columns: 1fr;
  }

  .modal-form .n-divider {
    margin: 20px 0;
  }
}

/* 特殊内容类型的弹窗样式 */

/* 图片预览弹窗 */
.modal-image-preview {
  width: 80vw;
  max-width: 1000px;
  height: 80vh;
}

.modal-image-preview .n-dialog__content {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 数据表格弹窗 */
.modal-data-table {
  width: 90vw;
  max-width: 1400px;
  height: 80vh;
}

.modal-data-table .n-dialog__content {
  padding: 16px;
  height: calc(80vh - 120px);
  overflow: hidden;
}

/* 详情查看弹窗 */
.modal-detail-view {
  width: 800px;
  max-width: 90vw;
  max-height: 80vh;
}

.modal-detail-view .n-dialog__content {
  max-height: calc(80vh - 120px);
  overflow-y: auto;
}

/* 确认对话框优化 */
.modal-confirm {
  width: 400px;
  max-width: 90vw;
}

.modal-confirm .n-dialog__content {
  padding: 24px;
  text-align: center;
}

.modal-confirm .n-dialog__action {
  justify-content: center;
  gap: 12px;
}

/* 表单验证错误时的样式 */
.modal-form .n-form-item--feedback-error {
  margin-bottom: 24px;
}

/* 加载状态样式 */
.modal-loading .n-dialog__content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 空状态样式 */
.modal-empty .n-dialog__content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  flex-direction: column;
}

/* 滚动条样式优化 */
.n-modal .n-dialog__content::-webkit-scrollbar {
  width: 6px;
}

.n-modal .n-dialog__content::-webkit-scrollbar-track {
  background: transparent;
}

.n-modal .n-dialog__content::-webkit-scrollbar-thumb {
  background: var(--n-scrollbar-color);
  border-radius: 3px;
}

.n-modal .n-dialog__content::-webkit-scrollbar-thumb:hover {
  background: var(--n-scrollbar-color-hover);
}
