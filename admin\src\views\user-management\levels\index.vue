<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref, computed, h } from 'vue';
import type { DataTableColumns } from 'naive-ui';
import { NButton, NTag, NPopconfirm, useMessage } from 'naive-ui';
// import { $t } from '@/locales';

defineOptions({
  name: 'UserLevels'
});

const message = useMessage();

interface LevelInfo {
  id: string;
  name: string;
  description: string;
  level: number;
  icon: string;
  color: string;
  minPoints: number;
  maxPoints: number;
  userCount: number;
  benefits: string[];
  upgradeRules: {
    pointsRequired: number;
    consumeRequired: number;
    timeRequired: number; // 天数
  };
  status: 'active' | 'disabled';
  createTime: string;
  updateTime: string;
}

// 模拟等级数据
const levels = ref<LevelInfo[]>([
  {
    id: '1',
    name: '新手用户',
    description: '刚注册的新用户',
    level: 1,
    icon: 'mdi:account',
    color: '#9ca3af',
    minPoints: 0,
    maxPoints: 99,
    userCount: 1234,
    benefits: ['基础购买权限', '客服支持'],
    upgradeRules: {
      pointsRequired: 100,
      consumeRequired: 0,
      timeRequired: 0
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '2',
    name: '普通用户',
    description: '完成首次购买的用户',
    level: 2,
    icon: 'mdi:account-check',
    color: '#3b82f6',
    minPoints: 100,
    maxPoints: 499,
    userCount: 8456,
    benefits: ['基础购买权限', '客服支持', '积分奖励'],
    upgradeRules: {
      pointsRequired: 500,
      consumeRequired: 500,
      timeRequired: 7
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '3',
    name: '银牌用户',
    description: '活跃的忠实用户',
    level: 3,
    icon: 'mdi:medal',
    color: '#6b7280',
    minPoints: 500,
    maxPoints: 1999,
    userCount: 3456,
    benefits: ['基础购买权限', '客服支持', '积分奖励', '9.5折优惠'],
    upgradeRules: {
      pointsRequired: 2000,
      consumeRequired: 2000,
      timeRequired: 30
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '4',
    name: '金牌用户',
    description: '高价值用户',
    level: 4,
    icon: 'mdi:medal',
    color: '#f59e0b',
    minPoints: 2000,
    maxPoints: 4999,
    userCount: 1234,
    benefits: ['基础购买权限', '客服支持', '积分奖励', '9折优惠', '专属客服'],
    upgradeRules: {
      pointsRequired: 5000,
      consumeRequired: 5000,
      timeRequired: 60
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '5',
    name: '钻石用户',
    description: '超级VIP用户',
    level: 5,
    icon: 'mdi:diamond',
    color: '#8b5cf6',
    minPoints: 5000,
    maxPoints: 9999,
    userCount: 567,
    benefits: ['基础购买权限', '客服支持', '积分奖励', '8.5折优惠', '专属客服', '生日礼品'],
    upgradeRules: {
      pointsRequired: 10000,
      consumeRequired: 10000,
      timeRequired: 90
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  },
  {
    id: '6',
    name: '至尊用户',
    description: '最高级别用户',
    level: 6,
    icon: 'mdi:crown',
    color: '#ef4444',
    minPoints: 10000,
    maxPoints: 999999,
    userCount: 123,
    benefits: ['基础购买权限', '客服支持', '积分奖励', '8折优惠', '专属客服', '生日礼品', '专属活动'],
    upgradeRules: {
      pointsRequired: 0,
      consumeRequired: 0,
      timeRequired: 0
    },
    status: 'active',
    createTime: '2024-01-01 00:00:00',
    updateTime: '2024-01-01 00:00:00'
  }
]);

// 显示创建/编辑等级模态框
const showLevelModal = ref(false);
const editingLevel = ref<LevelInfo | null>(null);

// 等级表单数据
const levelForm = ref({
  name: '',
  description: '',
  level: 1,
  icon: 'mdi:account',
  color: '#3b82f6',
  minPoints: 0,
  maxPoints: 99,
  benefits: [] as string[],
  upgradeRules: {
    pointsRequired: 0,
    consumeRequired: 0,
    timeRequired: 0
  }
});

// 图标选项
const iconOptions = [
  { label: '账户', value: 'mdi:account' },
  { label: '认证账户', value: 'mdi:account-check' },
  { label: '奖章', value: 'mdi:medal' },
  { label: '钻石', value: 'mdi:diamond' },
  { label: '皇冠', value: 'mdi:crown' },
  { label: '星星', value: 'mdi:star' }
];

// 权益选项
const benefitOptions = [
  '基础购买权限',
  '客服支持',
  '积分奖励',
  '9.5折优惠',
  '9折优惠',
  '8.5折优惠',
  '8折优惠',
  '专属客服',
  '生日礼品',
  '专属活动',
  '免费配送',
  '优先发货'
];

// 按等级排序的数据
const sortedLevels = computed(() => {
  return [...levels.value].sort((a, b) => a.level - b.level);
});

// 表格列定义
const columns = computed<DataTableColumns<LevelInfo>>(() => [
  {
    title: '等级',
    key: 'level',
    width: 80,
    render: (row) => h('span', { class: 'font-bold text-primary' }, row.level.toString())
  },
  {
    title: '等级名称',
    key: 'name',
    width: 150,
    render: (row) => h('div', { class: 'flex items-center space-x-8px' }, [
      h('div', {
        class: 'w-20px h-20px rd-full flex items-center justify-center',
        style: { backgroundColor: row.color }
      }, [
        h('span', { class: 'text-12px text-white font-bold' }, row.level.toString())
      ]),
      h('span', { class: 'font-medium' }, row.name)
    ])
  },
  {
    title: '等级描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '积分范围',
    key: 'pointsRange',
    width: 150,
    render: (row) => {
      if (row.level === 6) {
        return h('span', `${row.minPoints.toLocaleString()}+`);
      }
      return h('span', `${row.minPoints.toLocaleString()} - ${row.maxPoints.toLocaleString()}`);
    }
  },
  {
    title: '用户数量',
    key: 'userCount',
    width: 120,
    render: (row) => h('span', { class: 'font-medium text-primary' }, row.userCount.toLocaleString())
  },
  {
    title: '等级权益',
    key: 'benefits',
    width: 200,
    render: (row) => {
      return h('div', { class: 'flex flex-wrap gap-4px' }, 
        row.benefits.slice(0, 2).map(benefit => 
          h(NTag, { size: 'small', type: 'info' }, { default: () => benefit })
        ).concat(
          row.benefits.length > 2 ? [h(NTag, { size: 'small' }, { default: () => `+${row.benefits.length - 2}` })] : []
        )
      );
    }
  },
  {
    title: '升级规则',
    key: 'upgradeRules',
    width: 200,
    render: (row) => {
      if (row.level === 6) {
        return h('span', { class: 'text-gray-500' }, '最高等级');
      }
      return h('div', { class: 'text-12px space-y-2px' }, [
        h('div', `所需积分: ${row.upgradeRules.pointsRequired}`),
        h('div', `所需消费: ¥${row.upgradeRules.consumeRequired}`),
        h('div', `所需天数: ${row.upgradeRules.timeRequired}天`)
      ]);
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        active: { type: 'success' as const, label: '启用' },
        disabled: { type: 'error' as const, label: '禁用' }
      };
      const config = statusMap[row.status];
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row) => {
      return h('div', { class: 'flex items-center space-x-8px' }, [
        h(NButton, {
          size: 'small',
          type: 'primary',
          text: true,
          onClick: () => handleViewUsers(row.id)
        }, { default: () => '查看用户' }),
        h(NButton, {
          size: 'small',
          type: 'warning',
          text: true,
          onClick: () => handleEdit(row)
        }, { default: () => '编辑' }),
        h(NPopconfirm, {
          onPositiveClick: () => handleDelete(row.id)
        }, {
          default: () => '确认删除此等级？',
          trigger: () => h(NButton, {
            size: 'small',
            type: 'error',
            text: true,
            disabled: row.userCount > 0
          }, { default: () => '删除' })
        })
      ]);
    }
  }
]);

// 操作函数
function handleAdd() {
  editingLevel.value = null;
  const maxLevel = Math.max(...levels.value.map(l => l.level));
  levelForm.value = {
    name: '',
    description: '',
    level: maxLevel + 1,
    icon: 'mdi:account',
    color: '#3b82f6',
    minPoints: 0,
    maxPoints: 99,
    benefits: [],
    upgradeRules: {
      pointsRequired: 0,
      consumeRequired: 0,
      timeRequired: 0
    }
  };
  showLevelModal.value = true;
}

function handleEdit(level: LevelInfo) {
  editingLevel.value = level;
  levelForm.value = {
    name: level.name,
    description: level.description,
    level: level.level,
    icon: level.icon,
    color: level.color,
    minPoints: level.minPoints,
    maxPoints: level.maxPoints,
    benefits: [...level.benefits],
    upgradeRules: { ...level.upgradeRules }
  };
  showLevelModal.value = true;
}

function handleSave() {
  if (!levelForm.value.name.trim()) {
    message.error('等级名称不能为空');
    return;
  }
  
  if (editingLevel.value) {
    // 编辑等级
    const index = levels.value.findIndex(l => l.id === editingLevel.value!.id);
    if (index > -1) {
      levels.value[index] = {
        ...levels.value[index],
        ...levelForm.value,
        updateTime: new Date().toLocaleString()
      };
      message.success('等级更新成功');
    }
  } else {
    // 新增等级
    const newLevel: LevelInfo = {
      id: Date.now().toString(),
      ...levelForm.value,
      userCount: 0,
      status: 'active',
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    };
    levels.value.push(newLevel);
    message.success('等级创建成功');
  }
  
  showLevelModal.value = false;
}

function handleDelete(id: string) {
  const index = levels.value.findIndex(level => level.id === id);
  if (index > -1) {
    levels.value.splice(index, 1);
    message.success('删除成功');
  }
}

function handleViewUsers(id: string) {
  message.info('查看用户: ' + id);
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="16" class="p-16px">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
            用户等级
          </h1>
          <p class="text-14px text-gray-600 dark:text-gray-400">
            设置用户等级体系和升级规则
          </p>
        </div>
        <div class="flex items-center space-x-12px">
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <SvgIcon icon="mdi:plus" />
            </template>
            新增等级
          </NButton>
        </div>
      </div>

      <!-- 等级体系概览 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center">
            <SvgIcon icon="mdi:trophy" class="text-20px mr-8px text-primary" />
            <span class="text-16px font-medium">等级体系</span>
          </div>
        </template>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-16px">
          <div v-for="level in sortedLevels" :key="level.id" class="relative">
            <div 
              class="p-16px bg-gradient-to-br rd-12px text-white cursor-pointer transition-all duration-300 hover:scale-105"
              :style="{ backgroundImage: `linear-gradient(135deg, ${level.color}, ${level.color}dd)` }"
              @click="handleEdit(level)"
            >
              <div class="flex items-center justify-between mb-12px">
                <SvgIcon :icon="level.icon" class="text-24px" />
                <span class="text-12px opacity-80">LV.{{ level.level }}</span>
              </div>
              <h3 class="text-16px font-bold mb-4px">{{ level.name }}</h3>
              <p class="text-12px opacity-90 mb-8px line-height-relaxed">{{ level.description }}</p>
              <div class="flex items-center justify-between text-12px">
                <span>{{ level.userCount.toLocaleString() }} 用户</span>
                <span v-if="level.level < 6">{{ level.minPoints }}-{{ level.maxPoints }}</span>
                <span v-else>{{ level.minPoints }}+</span>
              </div>
            </div>
          </div>
        </div>
      </NCard>

      <!-- 等级列表表格 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <span class="text-16px font-medium">等级列表</span>
        </template>
        
        <NDataTable
          :columns="columns"
          :data="sortedLevels"
          :row-key="row => row.id"
          :scroll-x="1400"
          flex-height
          style="min-height: 400px"
        />
      </NCard>
    </NSpace>

    <!-- 创建/编辑等级模态框 -->
    <NModal
      v-model:show="showLevelModal"
      preset="dialog"
      :title="editingLevel ? '编辑等级' : '创建等级'"
      class="modal-extra-large modal-form modal-auto-height"
    >
      <NForm
        ref="levelFormRef"
        :model="levelForm"
        label-placement="left"
        label-width="120px"
        require-mark-placement="right-hanging"
      >
        <NGrid :x-gap="24" :y-gap="24" :cols="2">
          <NGi>
            <NFormItem label="等级名称" path="name" required>
              <NInput v-model="levelForm.name" placeholder="请输入等级名称" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="等级序号" path="level" required>
              <NInputNumber v-model="levelForm.level" :min="1" :max="99" class="w-full" />
            </NFormItem>
          </NGi>
          <NGi :span="2">
            <NFormItem label="等级描述" path="description">
              <NInput
                v-model="levelForm.description"
                type="textarea"
                placeholder="请输入等级描述"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="等级图标" path="icon">
              <NSelect
                v-model="levelForm.icon"
                :options="iconOptions"
                placeholder="选择等级图标"
              />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="等级颜色" path="color">
              <NColorPicker v-model="levelForm.color" :modes="['hex']" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="最小积分" path="minPoints">
              <NInputNumber v-model="levelForm.minPoints" :min="0" class="w-full" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="最大积分" path="maxPoints">
              <NInputNumber v-model="levelForm.maxPoints" :min="0" class="w-full" />
            </NFormItem>
          </NGi>
          <NGi :span="2">
            <NFormItem label="等级权益" path="benefits">
              <NCheckboxGroup v-model="levelForm.benefits">
                <NGrid :x-gap="16" :y-gap="12" :cols="3">
                  <NGi v-for="benefit in benefitOptions" :key="benefit">
                    <NCheckbox :value="benefit">{{ benefit }}</NCheckbox>
                  </NGi>
                </NGrid>
              </NCheckboxGroup>
            </NFormItem>
          </NGi>
        </NGrid>

        <NDivider class="my-24px">
          <span class="text-16px font-medium">升级规则</span>
        </NDivider>

        <NGrid :x-gap="24" :y-gap="24" :cols="3">
          <NGi>
            <NFormItem label="所需积分" path="upgradeRules.pointsRequired">
              <NInputNumber v-model="levelForm.upgradeRules.pointsRequired" :min="0" class="w-full" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="所需消费" path="upgradeRules.consumeRequired">
              <NInputNumber v-model="levelForm.upgradeRules.consumeRequired" :min="0" class="w-full" />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="所需天数" path="upgradeRules.timeRequired">
              <NInputNumber v-model="levelForm.upgradeRules.timeRequired" :min="0" class="w-full" />
            </NFormItem>
          </NGi>
        </NGrid>
      </NForm>
      
      <template #action>
        <NSpace>
          <NButton @click="showLevelModal = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.line-height-relaxed {
  line-height: 1.6;
}
</style>
