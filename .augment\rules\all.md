---
type: "always_apply"
---

技术栈分析
1. 后台管理系统 (admin目录)
框架: SoybeanAdmin v1.3.15
核心技术栈:

前端框架: Vue 3.5.17 + TypeScript 5.8.3
构建工具: Vite 7.0.4
UI组件库: Naive UI 2.42.0
CSS框架: UnoCSS 66.3.3
状态管理: Pinia 3.0.3
路由: Vue Router 4.5.1 + Elegant Router
国际化: Vue I18n 11.1.9
图表: ECharts 5.6.0
工具库: VueUse 13.5.0, dayjs 1.11.13
特色功能:

自动化文件路由系统
多主题配置支持
国际化方案
移动端适配
TypeScript严格类型检查
Monorepo架构 (pnpm workspace)
2. 后端服务 (server目录)
语言: Go 1.24
当前状态: 基础项目结构，仅有简单的Hello World示例

3. 移动端 (app目录)
框架: UniApp
支持平台:

微信小程序
支付宝小程序
百度小程序
字节跳动小程序
App (Android/iOS)
H5 当前状态: 基础项目结构
💡 推荐的技术栈补充
后端技术栈建议:
Web框架: Gin 或 Fiber
数据库:
MySQL 8.0+ (主数据库)
Redis (缓存、会话)
ORM: GORM
认证: JWT
文件存储:
本地存储 或
阿里云OSS/腾讯云COS
消息队列: Redis/RabbitMQ
日志: Logrus/Zap
配置管理: Viper
API文档: Swagger
容器化: Docker
数据库设计建议:
用户系统: 用户表、角色表、权限表
商品系统: 商品表、分类表、品牌表、规格表
订单系统: 订单表、订单详情表、支付表
库存系统: 库存表、库存日志表
营销系统: 优惠券表、活动表
第三方服务集成:
支付: 微信支付、支付宝
物流: 快递100、菜鸟
短信: 阿里云短信、腾讯云短信
推送: 极光推送、个推
🎯 开发建议
开发环境要求:
Node.js >= 20.19.0
pnpm >= 10.5.0
Go >= 1.24
MySQL 8.0+
Redis
开发顺序建议:
先完善后端API设计和数据库设计
然后开发后台管理系统的核心功能
最后开发移动端功能
代码规范:
后台管理系统已集成ESLint + Prettier
建议后端也使用golangci-lint
统一的Git提交规范