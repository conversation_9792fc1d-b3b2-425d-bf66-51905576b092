<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { ref } from 'vue';
import { useMessage } from 'naive-ui';
// import { $t } from '@/locales';

defineOptions({
  name: 'UserSettings'
});

const message = useMessage();

// 用户注册设置
const registerSettings = ref({
  allowRegister: true,
  requireEmailVerification: true,
  requirePhoneVerification: false,
  allowSocialLogin: true,
  defaultUserGroup: '普通用户',
  defaultUserLevel: 1,
  welcomeMessage: '欢迎加入FutureShop！',
  autoAssignTags: ['新用户'],
  registrationFields: {
    username: { required: true, minLength: 3, maxLength: 20 },
    email: { required: true },
    phone: { required: false },
    realName: { required: false },
    birthday: { required: false },
    gender: { required: false },
    address: { required: false }
  }
});

// 用户权限配置
const permissionSettings = ref({
  defaultPermissions: ['browse', 'purchase', 'comment'],
  guestPermissions: ['browse'],
  maxLoginAttempts: 5,
  lockoutDuration: 30, // 分钟
  sessionTimeout: 24, // 小时
  allowMultipleLogin: true,
  requirePasswordChange: false,
  passwordChangeInterval: 90, // 天
  twoFactorAuth: false
});

// 系统参数设置
const systemSettings = ref({
  pointsSystem: {
    enabled: true,
    registerPoints: 100,
    loginPoints: 1,
    purchasePointsRate: 0.01, // 每消费1元获得0.01积分
    pointsExpireDays: 365,
    minRedeemPoints: 100
  },
  levelSystem: {
    enabled: true,
    autoUpgrade: true,
    upgradeNotification: true
  },
  notification: {
    emailNotification: true,
    smsNotification: false,
    pushNotification: true,
    welcomeEmail: true,
    birthdayGreeting: true,
    levelUpNotification: true
  },
  privacy: {
    dataRetentionDays: 1095, // 3年
    allowDataExport: true,
    allowAccountDeletion: true,
    cookieConsent: true,
    privacyPolicyUrl: 'https://shop.bwzj.top/privacy',
    termsOfServiceUrl: 'https://shop.bwzj.top/terms'
  }
});

// 用户组选项
const userGroupOptions = [
  { label: '普通用户', value: '普通用户' },
  { label: 'VIP用户', value: 'VIP用户' },
  { label: '商家用户', value: '商家用户' }
];

// 用户等级选项
const userLevelOptions = [
  { label: '新手用户 (LV.1)', value: 1 },
  { label: '普通用户 (LV.2)', value: 2 },
  { label: '银牌用户 (LV.3)', value: 3 },
  { label: '金牌用户 (LV.4)', value: 4 },
  { label: '钻石用户 (LV.5)', value: 5 },
  { label: '至尊用户 (LV.6)', value: 6 }
];

// 权限选项
const permissionOptions = [
  { label: '浏览权限', value: 'browse' },
  { label: '购买权限', value: 'purchase' },
  { label: '评论权限', value: 'comment' },
  { label: '评价权限', value: 'review' },
  { label: '分享权限', value: 'share' },
  { label: '邀请权限', value: 'invite' }
];

// 标签选项
const tagOptions = [
  { label: '新用户', value: '新用户' },
  { label: '活跃用户', value: '活跃用户' },
  { label: '高价值客户', value: '高价值客户' },
  { label: '企业用户', value: '企业用户' }
];

// 保存设置
function handleSaveRegisterSettings() {
  message.success('保存成功');
}

function handleSavePermissionSettings() {
  message.success('保存成功');
}

function handleSaveSystemSettings() {
  message.success('保存成功');
}

// 重置设置
function handleResetRegisterSettings() {
  // 重置为默认值
  message.info('重置成功');
}

function handleResetPermissionSettings() {
  // 重置为默认值
  message.info('重置成功');
}

function handleResetSystemSettings() {
  // 重置为默认值
  message.info('重置成功');
}
</script>

<template>
  <div class="min-h-full overflow-auto">
    <NSpace vertical :size="24" class="p-16px">
      <!-- 页面标题 -->
      <div>
        <h1 class="text-24px font-bold text-gray-900 dark:text-gray-100 mb-8px">
          {{ $t('page.userManagement.settings.title') }}
        </h1>
        <p class="text-14px text-gray-600 dark:text-gray-400">
          {{ $t('page.userManagement.settings.description') }}
        </p>
      </div>

      <!-- 用户注册设置 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <SvgIcon icon="mdi:account-plus" class="text-20px mr-8px text-primary" />
              <span class="text-18px font-medium">{{ $t('page.userManagement.settings.registerSettings') }}</span>
            </div>
            <NSpace>
              <NButton @click="handleResetRegisterSettings">
                {{ $t('page.userManagement.settings.reset') }}
              </NButton>
              <NButton type="primary" @click="handleSaveRegisterSettings">
                {{ $t('page.userManagement.settings.save') }}
              </NButton>
            </NSpace>
          </div>
        </template>

        <NSpace vertical :size="20">
          <!-- 基础注册设置 -->
          <NGrid :x-gap="24" :y-gap="16" :cols="2">
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.allowRegister')">
                <NSwitch v-model:value="registerSettings.allowRegister" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.requireEmailVerification')">
                <NSwitch v-model:value="registerSettings.requireEmailVerification" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.requirePhoneVerification')">
                <NSwitch v-model:value="registerSettings.requirePhoneVerification" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.allowSocialLogin')">
                <NSwitch v-model:value="registerSettings.allowSocialLogin" />
              </NFormItem>
            </NGi>
          </NGrid>

          <!-- 默认设置 -->
          <NDivider>{{ $t('page.userManagement.settings.defaultSettings') }}</NDivider>
          <NGrid :x-gap="24" :y-gap="16" :cols="2">
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.defaultUserGroup')">
                <NSelect
                  v-model:value="registerSettings.defaultUserGroup"
                  :options="userGroupOptions"
                />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.defaultUserLevel')">
                <NSelect
                  v-model:value="registerSettings.defaultUserLevel"
                  :options="userLevelOptions"
                />
              </NFormItem>
            </NGi>
          </NGrid>

          <NFormItem :label="$t('page.userManagement.settings.welcomeMessage')">
            <NInput
              v-model:value="registerSettings.welcomeMessage"
              type="textarea"
              :rows="2"
              :placeholder="$t('page.userManagement.settings.welcomeMessagePlaceholder')"
            />
          </NFormItem>

          <NFormItem :label="$t('page.userManagement.settings.autoAssignTags')">
            <NSelect
              v-model:value="registerSettings.autoAssignTags"
              :options="tagOptions"
              multiple
              :placeholder="$t('page.userManagement.settings.selectTags')"
            />
          </NFormItem>

          <!-- 注册字段设置 -->
          <NDivider>{{ $t('page.userManagement.settings.registrationFields') }}</NDivider>
          <div class="space-y-12px">
            <div v-for="(field, key) in registerSettings.registrationFields" :key="key" class="flex items-center justify-between p-12px bg-gray-50 dark:bg-gray-800 rd-8px">
              <span class="font-medium">{{ $t(`page.userManagement.settings.fields.${key}`) }}</span>
              <NSpace>
                <NCheckbox v-model:checked="field.required">
                  {{ $t('page.userManagement.settings.required') }}
                </NCheckbox>
                <div v-if="field.minLength !== undefined" class="flex items-center space-x-8px">
                  <span class="text-12px">{{ $t('page.userManagement.settings.minLength') }}:</span>
                  <NInputNumber v-model:value="field.minLength" :min="1" :max="50" size="small" style="width: 80px" />
                </div>
                <div v-if="field.maxLength !== undefined" class="flex items-center space-x-8px">
                  <span class="text-12px">{{ $t('page.userManagement.settings.maxLength') }}:</span>
                  <NInputNumber v-model:value="field.maxLength" :min="1" :max="100" size="small" style="width: 80px" />
                </div>
              </NSpace>
            </div>
          </div>
        </NSpace>
      </NCard>

      <!-- 用户权限配置 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <SvgIcon icon="mdi:shield-account" class="text-20px mr-8px text-primary" />
              <span class="text-18px font-medium">{{ $t('page.userManagement.settings.permissionSettings') }}</span>
            </div>
            <NSpace>
              <NButton @click="handleResetPermissionSettings">
                {{ $t('page.userManagement.settings.reset') }}
              </NButton>
              <NButton type="primary" @click="handleSavePermissionSettings">
                {{ $t('page.userManagement.settings.save') }}
              </NButton>
            </NSpace>
          </div>
        </template>

        <NSpace vertical :size="20">
          <!-- 默认权限 -->
          <NFormItem :label="$t('page.userManagement.settings.defaultPermissions')">
            <NCheckboxGroup v-model:value="permissionSettings.defaultPermissions">
              <NSpace>
                <NCheckbox v-for="option in permissionOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </NCheckbox>
              </NSpace>
            </NCheckboxGroup>
          </NFormItem>

          <NFormItem :label="$t('page.userManagement.settings.guestPermissions')">
            <NCheckboxGroup v-model:value="permissionSettings.guestPermissions">
              <NSpace>
                <NCheckbox v-for="option in permissionOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </NCheckbox>
              </NSpace>
            </NCheckboxGroup>
          </NFormItem>

          <!-- 安全设置 -->
          <NDivider>{{ $t('page.userManagement.settings.securitySettings') }}</NDivider>
          <NGrid :x-gap="24" :y-gap="16" :cols="2">
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.maxLoginAttempts')">
                <NInputNumber v-model:value="permissionSettings.maxLoginAttempts" :min="1" :max="10" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.lockoutDuration')">
                <NInputNumber v-model:value="permissionSettings.lockoutDuration" :min="1" :max="1440" />
                <template #suffix>{{ $t('page.userManagement.settings.minutes') }}</template>
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.sessionTimeout')">
                <NInputNumber v-model:value="permissionSettings.sessionTimeout" :min="1" :max="168" />
                <template #suffix>{{ $t('page.userManagement.settings.hours') }}</template>
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.passwordChangeInterval')">
                <NInputNumber v-model:value="permissionSettings.passwordChangeInterval" :min="0" :max="365" />
                <template #suffix>{{ $t('page.userManagement.settings.days') }}</template>
              </NFormItem>
            </NGi>
          </NGrid>

          <NGrid :x-gap="24" :y-gap="16" :cols="2">
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.allowMultipleLogin')">
                <NSwitch v-model:value="permissionSettings.allowMultipleLogin" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.requirePasswordChange')">
                <NSwitch v-model:value="permissionSettings.requirePasswordChange" />
              </NFormItem>
            </NGi>
            <NGi>
              <NFormItem :label="$t('page.userManagement.settings.twoFactorAuth')">
                <NSwitch v-model:value="permissionSettings.twoFactorAuth" />
              </NFormItem>
            </NGi>
          </NGrid>
        </NSpace>
      </NCard>

      <!-- 系统参数设置 -->
      <NCard :bordered="false" class="card-wrapper">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <SvgIcon icon="mdi:cog" class="text-20px mr-8px text-primary" />
              <span class="text-18px font-medium">{{ $t('page.userManagement.settings.systemSettings') }}</span>
            </div>
            <NSpace>
              <NButton @click="handleResetSystemSettings">
                {{ $t('page.userManagement.settings.reset') }}
              </NButton>
              <NButton type="primary" @click="handleSaveSystemSettings">
                {{ $t('page.userManagement.settings.save') }}
              </NButton>
            </NSpace>
          </div>
        </template>

        <NSpace vertical :size="20">
          <!-- 积分系统 -->
          <div>
            <div class="flex items-center justify-between mb-16px">
              <h3 class="text-16px font-medium">{{ $t('page.userManagement.settings.pointsSystem') }}</h3>
              <NSwitch v-model:value="systemSettings.pointsSystem.enabled" />
            </div>
            <div v-if="systemSettings.pointsSystem.enabled" class="space-y-16px">
              <NGrid :x-gap="24" :y-gap="16" :cols="2">
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.registerPoints')">
                    <NInputNumber v-model:value="systemSettings.pointsSystem.registerPoints" :min="0" />
                  </NFormItem>
                </NGi>
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.loginPoints')">
                    <NInputNumber v-model:value="systemSettings.pointsSystem.loginPoints" :min="0" />
                  </NFormItem>
                </NGi>
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.purchasePointsRate')">
                    <NInputNumber v-model:value="systemSettings.pointsSystem.purchasePointsRate" :min="0" :max="1" :step="0.001" />
                  </NFormItem>
                </NGi>
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.pointsExpireDays')">
                    <NInputNumber v-model:value="systemSettings.pointsSystem.pointsExpireDays" :min="0" />
                  </NFormItem>
                </NGi>
              </NGrid>
              <NFormItem :label="$t('page.userManagement.settings.minRedeemPoints')">
                <NInputNumber v-model:value="systemSettings.pointsSystem.minRedeemPoints" :min="1" style="width: 200px" />
              </NFormItem>
            </div>
          </div>

          <!-- 等级系统 -->
          <NDivider />
          <div>
            <div class="flex items-center justify-between mb-16px">
              <h3 class="text-16px font-medium">{{ $t('page.userManagement.settings.levelSystem') }}</h3>
              <NSwitch v-model:value="systemSettings.levelSystem.enabled" />
            </div>
            <div v-if="systemSettings.levelSystem.enabled" class="space-y-16px">
              <NGrid :x-gap="24" :y-gap="16" :cols="2">
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.autoUpgrade')">
                    <NSwitch v-model:value="systemSettings.levelSystem.autoUpgrade" />
                  </NFormItem>
                </NGi>
                <NGi>
                  <NFormItem :label="$t('page.userManagement.settings.upgradeNotification')">
                    <NSwitch v-model:value="systemSettings.levelSystem.upgradeNotification" />
                  </NFormItem>
                </NGi>
              </NGrid>
            </div>
          </div>

          <!-- 通知设置 -->
          <NDivider />
          <div>
            <h3 class="text-16px font-medium mb-16px">{{ $t('page.userManagement.settings.notificationSettings') }}</h3>
            <NGrid :x-gap="24" :y-gap="16" :cols="2">
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.emailNotification')">
                  <NSwitch v-model:value="systemSettings.notification.emailNotification" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.smsNotification')">
                  <NSwitch v-model:value="systemSettings.notification.smsNotification" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.pushNotification')">
                  <NSwitch v-model:value="systemSettings.notification.pushNotification" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.welcomeEmail')">
                  <NSwitch v-model:value="systemSettings.notification.welcomeEmail" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.birthdayGreeting')">
                  <NSwitch v-model:value="systemSettings.notification.birthdayGreeting" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.levelUpNotification')">
                  <NSwitch v-model:value="systemSettings.notification.levelUpNotification" />
                </NFormItem>
              </NGi>
            </NGrid>
          </div>

          <!-- 隐私设置 -->
          <NDivider />
          <div>
            <h3 class="text-16px font-medium mb-16px">{{ $t('page.userManagement.settings.privacySettings') }}</h3>
            <NGrid :x-gap="24" :y-gap="16" :cols="2">
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.dataRetentionDays')">
                  <NInputNumber v-model:value="systemSettings.privacy.dataRetentionDays" :min="30" :max="3650" />
                  <template #suffix>{{ $t('page.userManagement.settings.days') }}</template>
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.allowDataExport')">
                  <NSwitch v-model:value="systemSettings.privacy.allowDataExport" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.allowAccountDeletion')">
                  <NSwitch v-model:value="systemSettings.privacy.allowAccountDeletion" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.cookieConsent')">
                  <NSwitch v-model:value="systemSettings.privacy.cookieConsent" />
                </NFormItem>
              </NGi>
            </NGrid>
            <NGrid :x-gap="24" :y-gap="16" :cols="2">
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.privacyPolicyUrl')">
                  <NInput v-model:value="systemSettings.privacy.privacyPolicyUrl" />
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem :label="$t('page.userManagement.settings.termsOfServiceUrl')">
                  <NInput v-model:value="systemSettings.privacy.termsOfServiceUrl" />
                </NFormItem>
              </NGi>
            </NGrid>
          </div>
        </NSpace>
      </NCard>
    </NSpace>
  </div>
</template>

<style scoped></style>
