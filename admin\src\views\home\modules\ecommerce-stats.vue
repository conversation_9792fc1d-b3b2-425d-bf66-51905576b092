<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { computed } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';

defineOptions({
  name: 'EcommerceStats'
});

interface StatCard {
  key: string;
  title: string;
  value: number;
  unit: string;
  growth: number;
  color: {
    start: string;
    end: string;
  };
  icon: string;
}

const statsData = computed<StatCard[]>(() => [
  {
    key: 'totalSales',
    title: $t('page.home.totalSales'),
    value: 1256789,
    unit: '¥',
    growth: 12.5,
    color: {
      start: '#4ade80',
      end: '#22c55e'
    },
    icon: 'mdi:currency-cny'
  },
  {
    key: 'totalOrders',
    title: $t('page.home.totalOrders'),
    value: 8642,
    unit: '',
    growth: 8.3,
    color: {
      start: '#3b82f6',
      end: '#1d4ed8'
    },
    icon: 'mdi:shopping'
  },
  {
    key: 'totalUsers',
    title: $t('page.home.totalUsers'),
    value: 15678,
    unit: '',
    growth: 15.2,
    color: {
      start: '#f59e0b',
      end: '#d97706'
    },
    icon: 'mdi:account-group'
  },
  {
    key: 'totalProducts',
    title: $t('page.home.totalProducts'),
    value: 2345,
    unit: '',
    growth: 5.7,
    color: {
      start: '#8b5cf6',
      end: '#7c3aed'
    },
    icon: 'mdi:package-variant'
  }
]);

interface GradientBgProps {
  gradientColor: string;
}

const [DefineGradientBg, GradientBg] = createReusableTemplate<GradientBgProps>();

function getGradientColor(color: StatCard['color']) {
  return `linear-gradient(135deg, ${color.start}, ${color.end})`;
}

function formatNumber(num: number): string {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w';
  }
  return num.toLocaleString();
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <!-- define component start: GradientBg -->
    <DefineGradientBg v-slot="{ $slots, gradientColor }">
      <div class="rd-12px px-20px py-16px text-white" :style="{ backgroundImage: gradientColor }">
        <component :is="$slots.default" />
      </div>
    </DefineGradientBg>
    <!-- define component end: GradientBg -->

    <NGrid cols="s:1 m:2 l:4" responsive="screen" :x-gap="16" :y-gap="16">
      <NGi v-for="item in statsData" :key="item.key">
        <GradientBg :gradient-color="getGradientColor(item.color)" class="flex-1">
          <div class="flex items-center justify-between mb-12px">
            <h3 class="text-16px font-medium">{{ item.title }}</h3>
            <SvgIcon :icon="item.icon" class="text-28px opacity-80" />
          </div>
          <div class="flex items-end justify-between">
            <div>
              <div class="text-28px font-bold mb-4px">
                {{ item.unit }}{{ formatNumber(item.value) }}
              </div>
              <div class="flex items-center text-12px opacity-90">
                <SvgIcon 
                  :icon="item.growth > 0 ? 'mdi:trending-up' : 'mdi:trending-down'" 
                  class="text-14px mr-4px"
                />
                <span>{{ Math.abs(item.growth) }}%</span>
                <span class="ml-4px">{{ $t('page.home.vsLastMonth') }}</span>
              </div>
            </div>
          </div>
        </GradientBg>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
