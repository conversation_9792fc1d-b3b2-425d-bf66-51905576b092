#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules/npm-check-updates/build/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules/npm-check-updates/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules/npm-check-updates/build/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules/npm-check-updates/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/npm-check-updates@18.0.1/node_modules:/mnt/d/CodeFile/codeproject/futureshop/admin/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../npm-check-updates/build/cli.js" "$@"
else
  exec node  "$basedir/../npm-check-updates/build/cli.js" "$@"
fi
