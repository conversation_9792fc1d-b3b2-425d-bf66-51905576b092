/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'user-management',
    path: '/user-management',
    component: 'layout.base',
    meta: {
      title: 'user-management',
      i18nKey: 'route.user-management'
    },
    children: [
      {
        name: 'user-management_groups',
        path: '/user-management/groups',
        component: 'view.user-management_groups',
        meta: {
          title: 'user-management_groups',
          i18nKey: 'route.user-management_groups'
        }
      },
      {
        name: 'user-management_levels',
        path: '/user-management/levels',
        component: 'view.user-management_levels',
        meta: {
          title: 'user-management_levels',
          i18nKey: 'route.user-management_levels'
        }
      },
      {
        name: 'user-management_settings',
        path: '/user-management/settings',
        component: 'view.user-management_settings',
        meta: {
          title: 'user-management_settings',
          i18nKey: 'route.user-management_settings'
        }
      },
      {
        name: 'user-management_statistics',
        path: '/user-management/statistics',
        component: 'view.user-management_statistics',
        meta: {
          title: 'user-management_statistics',
          i18nKey: 'route.user-management_statistics'
        }
      },
      {
        name: 'user-management_tags',
        path: '/user-management/tags',
        component: 'view.user-management_tags',
        meta: {
          title: 'user-management_tags',
          i18nKey: 'route.user-management_tags'
        }
      },
      {
        name: 'user-management_users',
        path: '/user-management/users',
        component: 'view.user-management_users',
        meta: {
          title: 'user-management_users',
          i18nKey: 'route.user-management_users'
        }
      }
    ]
  }
];
