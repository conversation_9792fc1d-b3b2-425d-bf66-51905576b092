import type { CustomRoute, ElegantConstRoute, ElegantRoute } from '@elegant-router/types';
import { generatedRoutes } from '../elegant/routes';
import { layouts, views } from '../elegant/imports';
import { transformElegantRoutesToVueRoutes } from '../elegant/transform';

/**
 * custom routes
 *
 * @link https://github.com/soybeanjs/elegant-router?tab=readme-ov-file#custom-route
 */
const customRoutes: CustomRoute[] = [
  // 用户管理父级菜单（包含子菜单）
  {
    name: 'user-management',
    path: '/user-management',
    component: 'layout.base',
    redirect: '/user-management/statistics',
    meta: {
      title: '用户管理',
      i18nKey: 'route.user-management',
      icon: 'mdi:account-group',
      order: 2
    },
    children: [
      // 用户统计
      {
        name: 'user-management_statistics',
        path: '/user-management/statistics',
        component: 'view.user-management_statistics',
        meta: {
          title: '用户统计',
          i18nKey: 'route.user-management_statistics',
          icon: 'mdi:chart-line',
          order: 1
        }
      },
      // 用户管理
      {
        name: 'user-management_users',
        path: '/user-management/users',
        component: 'view.user-management_users',
        meta: {
          title: '用户管理',
          i18nKey: 'route.user-management_users',
          icon: 'mdi:account-group-outline',
          order: 2
        }
      },
      // 用户分组
      {
        name: 'user-management_groups',
        path: '/user-management/groups',
        component: 'view.user-management_groups',
        meta: {
          title: '用户分组',
          i18nKey: 'route.user-management_groups',
          icon: 'mdi:account-multiple-outline',
          order: 3
        }
      },
      // 用户标签
      {
        name: 'user-management_tags',
        path: '/user-management/tags',
        component: 'view.user-management_tags',
        meta: {
          title: '用户标签',
          i18nKey: 'route.user-management_tags',
          icon: 'mdi:tag-multiple-outline',
          order: 4
        }
      },
      // 用户等级
      {
        name: 'user-management_levels',
        path: '/user-management/levels',
        component: 'view.user-management_levels',
        meta: {
          title: '用户等级',
          i18nKey: 'route.user-management_levels',
          icon: 'mdi:star-circle-outline',
          order: 5
        }
      },
      // 用户配置
      {
        name: 'user-management_settings',
        path: '/user-management/settings',
        component: 'view.user-management_settings',
        meta: {
          title: '用户配置',
          i18nKey: 'route.user-management_settings',
          icon: 'mdi:cog-outline',
          order: 6
        }
      }
    ]
  }
];

/** create routes when the auth route mode is static */
export function createStaticRoutes() {
  const constantRoutes: ElegantRoute[] = [];
  const authRoutes: ElegantRoute[] = [];

  // 创建一个Map来存储路由，确保customRoutes优先级更高
  const routeMap = new Map<string, ElegantRoute>();

  // 先添加generatedRoutes
  generatedRoutes.forEach(item => {
    routeMap.set(item.name, item);
  });

  // 再添加customRoutes，会覆盖同名的generatedRoutes
  customRoutes.forEach(item => {
    routeMap.set(item.name, item);
  });

  // 将Map转换为数组并分类
  Array.from(routeMap.values()).forEach(item => {
    if (item.meta?.constant) {
      constantRoutes.push(item);
    } else {
      authRoutes.push(item);
    }
  });

  return {
    constantRoutes,
    authRoutes
  };
}

/**
 * Get auth vue routes
 *
 * @param routes Elegant routes
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  return transformElegantRoutesToVueRoutes(routes, layouts, views);
}
