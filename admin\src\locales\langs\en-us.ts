const local: App.I18n.Schema = {
  system: {
    title: 'FutureShopAdmin',
    updateTitle: 'System Version Update Notification',
    updateContent: 'A new version of the system has been detected. Do you want to refresh the page immediately?',
    updateConfirm: 'Refresh immediately',
    updateCancel: 'Later'
  },
  common: {
    action: 'Action',
    add: 'Add',
    addSuccess: 'Add Success',
    backToHome: 'Back to home',
    batchDelete: 'Batch Delete',
    cancel: 'Cancel',
    close: 'Close',
    check: 'Check',
    expandColumn: 'Expand Column',
    columnSetting: 'Column Setting',
    config: 'Config',
    confirm: 'Confirm',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    confirmDelete: 'Are you sure you want to delete?',
    edit: 'Edit',
    warning: 'Warning',
    error: 'Error',
    index: 'Index',
    keywordSearch: 'Please enter keyword',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to log out?',
    lookForward: 'Coming soon',
    modify: 'Modify',
    modifySuccess: 'Modify Success',
    more: 'More',
    noData: 'No Data',
    operate: 'Operate',
    pleaseCheckValue: 'Please check whether the value is valid',
    refresh: 'Refresh',
    reset: 'Reset',
    search: 'Search',
    switch: 'Switch',
    tip: 'Tip',
    trigger: 'Trigger',
    update: 'Update',
    updateSuccess: 'Update Success',
    userCenter: 'User Center',
    yesOrNo: {
      yes: 'Yes',
      no: 'No'
    }
  },
  request: {
    logout: 'Logout user after request failed',
    logoutMsg: 'User status is invalid, please log in again',
    logoutWithModal: 'Pop up modal after request failed and then log out user',
    logoutWithModalMsg: 'User status is invalid, please log in again',
    refreshToken: 'The requested token has expired, refresh the token',
    tokenExpired: 'The requested token has expired'
  },
  theme: {
    themeSchema: {
      title: 'Theme Schema',
      light: 'Light',
      dark: 'Dark',
      auto: 'Follow System'
    },
    grayscale: 'Grayscale',
    colourWeakness: 'Colour Weakness',
    layoutMode: {
      title: 'Layout Mode',
      vertical: 'Vertical Menu Mode',
      horizontal: 'Horizontal Menu Mode',
      'vertical-mix': 'Vertical Mix Menu Mode',
      'horizontal-mix': 'Horizontal Mix menu Mode',
      reverseHorizontalMix: 'Reverse first level menus and child level menus position'
    },
    recommendColor: 'Apply Recommended Color Algorithm',
    recommendColorDesc: 'The recommended color algorithm refers to',
    themeColor: {
      title: 'Theme Color',
      primary: 'Primary',
      info: 'Info',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      followPrimary: 'Follow Primary'
    },
    scrollMode: {
      title: 'Scroll Mode',
      wrapper: 'Wrapper',
      content: 'Content'
    },
    page: {
      animate: 'Page Animate',
      mode: {
        title: 'Page Animate Mode',
        fade: 'Fade',
        'fade-slide': 'Slide',
        'fade-bottom': 'Fade Zoom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'None'
      }
    },
    fixedHeaderAndTab: 'Fixed Header And Tab',
    header: {
      height: 'Header Height',
      breadcrumb: {
        visible: 'Breadcrumb Visible',
        showIcon: 'Breadcrumb Icon Visible'
      },
      multilingual: {
        visible: 'Display multilingual button'
      },
      globalSearch: {
        visible: 'Display GlobalSearch button'
      }
    },
    tab: {
      visible: 'Tab Visible',
      cache: 'Tag Bar Info Cache',
      height: 'Tab Height',
      mode: {
        title: 'Tab Mode',
        chrome: 'Chrome',
        button: 'Button'
      }
    },
    sider: {
      inverted: 'Dark Sider',
      width: 'Sider Width',
      collapsedWidth: 'Sider Collapsed Width',
      mixWidth: 'Mix Sider Width',
      mixCollapsedWidth: 'Mix Sider Collapse Width',
      mixChildMenuWidth: 'Mix Child Menu Width'
    },
    footer: {
      visible: 'Footer Visible',
      fixed: 'Fixed Footer',
      height: 'Footer Height',
      right: 'Right Footer'
    },
    watermark: {
      visible: 'Watermark Full Screen Visible',
      text: 'Watermark Text',
      enableUserName: 'Enable User Name Watermark'
    },
    themeDrawerTitle: 'Theme Configuration',
    pageFunTitle: 'Page Function',
    resetCacheStrategy: {
      title: 'Reset Cache Strategy',
      close: 'Close Page',
      refresh: 'Refresh Page'
    },
    configOperation: {
      copyConfig: 'Copy Config',
      copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
      resetConfig: 'Reset Config',
      resetSuccessMsg: 'Reset Success'
    }
  },
  route: {
    login: 'Login',
    403: 'No Permission',
    404: 'Page Not Found',
    500: 'Server Error',
    'iframe-page': 'Iframe',
    home: 'Home',
    'user-management': 'User Management',
    'user-management_statistics': 'User Statistics',
    'user-management_users': 'User Management',
    'user-management_groups': 'User Groups',
    'user-management_tags': 'User Tags',
    'user-management_levels': 'User Levels',
    'user-management_settings': 'User Settings'
  },
  page: {
    login: {
      common: {
        loginOrRegister: 'Login / Register',
        userNamePlaceholder: 'Please enter user name',
        phonePlaceholder: 'Please enter phone number',
        codePlaceholder: 'Please enter verification code',
        passwordPlaceholder: 'Please enter password',
        confirmPasswordPlaceholder: 'Please enter password again',
        codeLogin: 'Verification code login',
        confirm: 'Confirm',
        back: 'Back',
        validateSuccess: 'Verification passed',
        loginSuccess: 'Login successfully',
        welcomeBack: 'Welcome back, {userName} !'
      },
      pwdLogin: {
        title: 'Password Login',
        rememberMe: 'Remember me',
        forgetPassword: 'Forget password?',
        register: 'Register',
        otherAccountLogin: 'Other Account Login',
        otherLoginMode: 'Other Login Mode',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        user: 'User'
      },
      codeLogin: {
        title: 'Verification Code Login',
        getCode: 'Get verification code',
        reGetCode: 'Reacquire after {time}s',
        sendCodeSuccess: 'Verification code sent successfully',
        imageCodePlaceholder: 'Please enter image verification code'
      },
      register: {
        title: 'Register',
        agreement: 'I have read and agree to',
        protocol: '《User Agreement》',
        policy: '《Privacy Policy》'
      },
      resetPwd: {
        title: 'Reset Password'
      },
      bindWeChat: {
        title: 'Bind WeChat'
      }
    },
    home: {
      branchDesc:
        'For the convenience of everyone in developing and updating the merge, we have streamlined the code of the main branch, only retaining the homepage menu, and the rest of the content has been moved to the example branch for maintenance. The preview address displays the content of the example branch.',
      welcome: 'Welcome to',
      dashboard: 'Admin Dashboard',
      currentUser: 'Current User',
      userRole: 'User Role',
      lastLogin: 'Last Login',
      todayOrders: 'Today\'s Orders',
      todaySales: 'Today\'s Sales',
      todayUsers: 'Today\'s New Users',
      quickActions: 'Quick Actions',
      newOrder: 'New Order',
      productManagement: 'Product Management',
      salesReport: 'Sales Report',
      roles: {
        admin: 'Administrator',
        manager: 'Manager',
        user: 'User',
        guest: 'Guest'
      },
      totalSales: 'Total Sales',
      totalOrders: 'Total Orders',
      totalUsers: 'Total Users',
      totalProducts: 'Total Products',
      vsLastMonth: 'vs Last Month',
      salesTrend: 'Sales Trend',
      salesAmount: 'Sales Amount',
      orderAmount: 'Order Amount',
      recentOrders: 'Recent Orders',
      viewAll: 'View All',
      viewMoreOrders: 'View More Orders',
      orderStatus: {
        pending: 'Pending',
        paid: 'Paid',
        shipped: 'Shipped',
        completed: 'Completed',
        cancelled: 'Cancelled'
      },
      hotProducts: 'Hot Products',
      sales: 'Sales',
      viewMoreProducts: 'View More Products',
      systemNotices: 'System Notices',
      markAllRead: 'Mark All Read',
      viewAllNotices: 'View All Notices',
      minutesAgo: ' minutes ago',
      hoursAgo: ' hours ago',
      daysAgo: ' days ago',
      sampleCustomer1: 'Mr. Zhang',
      sampleCustomer2: 'Ms. Li',
      sampleCustomer3: 'Mr. Wang',
      sampleCustomer4: 'Ms. Zhao',
      sampleCustomer5: 'Mr. Qian',
      sampleProduct1: 'iPhone 15 Pro Max 256GB',
      sampleProduct2: 'MacBook Pro 14" M3 Chip',
      sampleProduct3: 'AirPods Pro 3rd Gen',
      sampleProduct4: 'iPad Air 5th Gen 256GB',
      sampleProduct5: 'Apple Watch Series 9',
      notice: {
        systemUpdate: 'System Update Notice',
        systemUpdateContent: 'The system will be updated at 22:00 tonight, with an estimated maintenance time of 30 minutes. Please prepare in advance.',
        lowStock: 'Low Stock Alert',
        lowStockContent: '5 products have less than 10 items in stock. Please restock in time to avoid affecting sales.',
        newOrder: 'New Order Reminder',
        newOrderContent: 'You have 3 new orders to process. Please check and handle them in time.',
        paymentFailed: 'Payment Failed Notice',
        paymentFailedContent: 'Payment for order FS202501010006 failed. Please contact the customer to pay again.',
        promotion: 'Promotion Activity',
        promotionContent: 'The Spring Festival promotion is about to start. Please prepare related products and marketing materials in advance.'
      },
      greeting: 'Good morning, {userName}, today is another day full of vitality!',
      weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
      projectCount: 'Project Count',
      todo: 'Todo',
      message: 'Message',
      downloadCount: 'Download Count',
      registerCount: 'Register Count',
      schedule: 'Work and rest Schedule',
      study: 'Study',
      work: 'Work',
      rest: 'Rest',
      entertainment: 'Entertainment',
      visitCount: 'Visit Count',
      turnover: 'Turnover',
      dealCount: 'Deal Count',
      projectNews: {
        title: 'Project News',
        moreNews: 'More News',
        desc1: 'Soybean created the open source project soybean-admin on May 28, 2021!',
        desc2: 'Yanbowe submitted a bug to soybean-admin, the multi-tab bar will not adapt.',
        desc3: 'Soybean is ready to do sufficient preparation for the release of soybean-admin!',
        desc4: 'Soybean is busy writing project documentation for soybean-admin!',
        desc5: 'Soybean just wrote some of the workbench pages casually, and it was enough to see!'
      },
      creativity: 'Creativity'
    },
    userManagement: {
      title: 'User Management',
      description: 'Manage system users, groups, tags, levels and permission settings',
      addUser: 'Add User',
      export: 'Export Data',
      modules: {
        title: 'Function Modules'
      },
      overview: {
        title: 'Data Overview',
        totalUsers: 'Total Users',
        activeUsers: 'Active Users',
        newUsers: 'New Users Today',
        vipUsers: 'VIP Users'
      },
      enterModule: 'Enter Module',
      statistics: {
        title: 'User Statistics',
        description: 'View user growth trends, source analysis and regional distribution statistics',
        totalUsers: 'Total Users',
        activeUsers: 'Active Users',
        newUsers: 'New Users',
        vipUsers: 'VIP Users',
        today: 'Today',
        userGrowthTrend: 'User Growth Trend',
        userSourceAnalysis: 'User Source Analysis',
        userSource: 'User Source',
        regionDistribution: 'Regional Distribution',
        timeRange: {
          '7d': 'Last 7 Days',
          '30d': 'Last 30 Days',
          '90d': 'Last 90 Days',
          '1y': 'Last Year'
        },
        sources: {
          organic: 'Organic Search',
          social: 'Social Media',
          direct: 'Direct Access',
          referral: 'Referral Links',
          email: 'Email Marketing',
          ads: 'Advertising'
        },
        regions: {
          beijing: 'Beijing',
          shanghai: 'Shanghai',
          guangzhou: 'Guangzhou',
          shenzhen: 'Shenzhen',
          hangzhou: 'Hangzhou',
          others: 'Other Regions'
        }
      },
      users: {
        title: 'User Management',
        description: 'View and manage all system user information',
        addUser: 'Add User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        viewUser: 'View User',
        exportData: 'Export Data',
        search: 'Search',
        reset: 'Reset',
        searchPlaceholder: 'Search username, email or phone',
        selectStatus: 'Select Status',
        selectLevel: 'Select Level',
        selectGroup: 'Select Group',
        advancedFilter: 'Advanced Filter',
        resetFilter: 'Reset Filter',
        filter: {
          userIdentity: 'User Identity',
          paidMember: 'Paid Member',
          balanceRange: 'Balance Range',
          pointsRange: 'Points Range',
          lastConsumption: 'Last Consumption',
          orderCount: 'Order Count',
          totalSpent: 'Total Spent',
          rechargeCount: 'Recharge Count',
          lastLogin: 'Last Login',
          selectTags: 'Select Tags',
          selectIdentity: 'Select Identity',
          selectMemberStatus: 'Select Member Status',
          selectBalanceRange: 'Select Balance Range',
          selectPointsRange: 'Select Points Range',
          selectConsumptionTime: 'Select Consumption Time',
          selectOrderRange: 'Select Order Range',
          selectSpentRange: 'Select Spent Range',
          selectRechargeRange: 'Select Recharge Range',
          selectLoginTime: 'Select Login Time'
        },
        filterOptions: {
          identity: {
            normal: 'Normal User',
            merchant: 'Merchant',
            agent: 'Agent',
            admin: 'Administrator'
          },
          memberStatus: {
            yes: 'Yes',
            no: 'No'
          },
          balanceRange: {
            range1: '$0-100',
            range2: '$100-500',
            range3: '$500-1000',
            range4: '$1000-5000',
            range5: 'Over $5000'
          },
          pointsRange: {
            range1: '0-1000 pts',
            range2: '1000-5000 pts',
            range3: '5000-10000 pts',
            range4: 'Over 10000 pts'
          },
          consumptionTime: {
            days7: 'Within 7 days',
            days30: 'Within 30 days',
            days90: 'Within 90 days',
            days180: 'Within 180 days',
            never: 'Never consumed'
          },
          orderRange: {
            range1: '0-5 orders',
            range2: '5-20 orders',
            range3: '20-50 orders',
            range4: 'Over 50 orders'
          },
          spentRange: {
            range1: '$0-500',
            range2: '$500-2000',
            range3: '$2000-5000',
            range4: '$5000-10000',
            range5: 'Over $10000'
          },
          rechargeRange: {
            range1: '0 times',
            range2: '1-5 times',
            range3: '5-20 times',
            range4: 'Over 20 times'
          },
          loginTime: {
            today: 'Today',
            days7: 'Within 7 days',
            days30: 'Within 30 days',
            days90: 'Within 90 days',
            never: 'Never logged in'
          }
        },
        batchEnable: 'Batch Enable',
        batchDisable: 'Batch Disable',
        batchDelete: 'Batch Delete',
        selectedCount: 'Selected {count} items',
        totalRecords: 'Total {total} records',
        showingRecords: 'Showing {start} - {end} of {total} records',
        confirmDelete: 'Confirm to delete this user?',
        confirmBatchDelete: 'Will delete {count} users, this operation cannot be undone',
        deleteSuccess: 'Delete successful',
        batchEnableSuccess: 'Batch enable successful',
        batchDisableSuccess: 'Batch disable successful',
        pleaseSelectUsers: 'Please select users first',
        exportingData: 'Exporting data...',
        columns: {
          avatar: 'Avatar',
          username: 'Username',
          email: 'Email',
          phone: 'Phone',
          status: 'Status',
          level: 'Level',
          group: 'Group',
          tags: 'Tags',
          orderCount: 'Orders',
          totalSpent: 'Total Spent',
          registerTime: 'Register Time',
          lastLoginTime: 'Last Login',
          actions: 'Actions'
        },
        status: {
          active: 'Active',
          disabled: 'Disabled',
          pending: 'Pending'
        },
        statusActions: {
          enable: 'Enable',
          disable: 'Disable'
        },
        actions: {
          view: 'View',
          edit: 'Edit',
          delete: 'Delete',
          more: 'More Actions',
          modifyBalance: 'Modify Balance',
          modifyPoints: 'Modify Points',
          giftMembership: 'Gift Membership',
          setGroup: 'Set Group',
          setTags: 'Set Tags',
          setPromoter: 'Set Promoter'
        },
        level: {
          normal: 'Normal User',
          vip: 'VIP User',
          svip: 'Super VIP'
        },
        group: {
          normal: 'Normal User',
          new: 'New User',
          vip: 'VIP User'
        },
        form: {
          username: 'Username',
          email: 'Email',
          phone: 'Phone',
          password: 'Password',
          confirmPassword: 'Confirm Password',
          status: 'Status',
          level: 'Level',
          group: 'Group',
          tags: 'Tags',
          avatar: 'Avatar',
          usernamePlaceholder: 'Please enter username',
          emailPlaceholder: 'Please enter email address',
          phonePlaceholder: 'Please enter phone number',
          passwordPlaceholder: 'Please enter password',
          confirmPasswordPlaceholder: 'Please confirm password',
          tagsPlaceholder: 'Enter tags, press Enter to add',
          usernameRequired: 'Please enter username',
          emailRequired: 'Please enter email address',
          emailInvalid: 'Please enter a valid email address',
          phoneRequired: 'Please enter phone number',
          phoneInvalid: 'Please enter a valid phone number',
          passwordRequired: 'Please enter password',
          passwordMinLength: 'Password must be at least 6 characters',
          confirmPasswordRequired: 'Please confirm password',
          passwordNotMatch: 'Passwords do not match',
          uploadAvatar: 'Upload Avatar',
          avatarUploadTip: 'Click or drag file to this area to upload',
          avatarUploadDesc: 'Support JPG, PNG format, file size no more than 2MB',
          avatarUploadSuccess: 'Avatar uploaded successfully',
          avatarUploadError: 'Avatar upload failed'
        },
        userDetail: {
          title: 'User Details',
          tabs: {
            userInfo: 'User Info',
            consumptionRecords: 'Consumption Records',
            pointsDetail: 'Points Detail',
            signInRecords: 'Sign-in Records',
            coupons: 'Coupons',
            balanceChanges: 'Balance Changes',
            friendRelations: 'Friend Relations'
          },
          overview: {
            title: 'User Overview',
            balance: 'Balance',
            points: 'Points',
            totalOrders: 'Total Orders',
            totalSpent: 'Total Spent',
            monthlyOrders: 'Monthly Orders',
            monthlySpent: 'Monthly Spent'
          },
          basicInfo: {
            title: 'Basic Information',
            userId: 'User ID',
            realName: 'Real Name',
            birthday: 'Birthday',
            idCard: 'ID Card',
            address: 'Address'
          },
          accountInfo: {
            title: 'Account Information',
            loginPassword: 'Login Password'
          },
          userProfile: {
            title: 'User Profile',
            promotionQualification: 'Promotion Qualification',
            userStatus: 'User Status',
            userLevel: 'User Level',
            userTags: 'User Tags',
            userGroup: 'User Group',
            promoter: 'Promoter',
            registerTime: 'Register Time',
            lastLoginTime: 'Last Login Time'
          },
          userNotes: {
            title: 'User Notes',
            placeholder: 'Please enter user notes',
            save: 'Save Notes'
          },
          consumptionRecords: {
            title: 'Consumption Records',
            orderId: 'Order ID',
            receiver: 'Receiver',
            quantity: 'Quantity',
            actualAmount: 'Actual Amount',
            completionTime: 'Completion Time'
          },
          pointsDetail: {
            title: 'Points Detail',
            source: 'Source/Purpose',
            change: 'Points Change',
            afterChange: 'After Change',
            date: 'Date',
            remark: 'Remark'
          },
          signInRecords: {
            title: 'Sign-in Records',
            signInDate: 'Sign-in Date',
            consecutiveDays: 'Consecutive Days',
            pointsEarned: 'Points Earned'
          },
          coupons: {
            title: 'Coupons',
            name: 'Coupon Name',
            type: 'Type',
            amount: 'Amount',
            condition: 'Condition',
            validity: 'Validity',
            status: 'Status'
          },
          balanceChanges: {
            title: 'Balance Changes',
            changeType: 'Change Type',
            changeAmount: 'Change Amount',
            afterChange: 'After Change',
            time: 'Time',
            remark: 'Remark'
          },
          friendRelations: {
            title: 'Friend Relations',
            friendUsername: 'Friend Username',
            relationType: 'Relation Type',
            establishTime: 'Establish Time'
          },
          quickActions: {
            title: 'Quick Actions',
            modifyBalance: 'Modify Balance',
            modifyPoints: 'Modify Points',
            giftCoupon: 'Gift Coupon',
            giftMembership: 'Gift Membership',
            setTags: 'Set Tags',
            setGroup: 'Set Group',
            setPromoter: 'Set Promoter',
            editNotes: 'Edit Notes'
          }
        }
      },
      groups: {
        title: 'User Groups',
        description: 'Create and manage user groups, configure group permissions',
        createGroup: 'Create Group',
        editGroup: 'Edit Group',
        search: 'Search',
        reset: 'Reset',
        advancedFilter: 'Advanced Filter',
        exportData: 'Export Data',
        searchPlaceholder: 'Search group name or description',
        selectStatus: 'Select Status',
        selectedCount: 'Selected {count} items',
        totalCount: 'Total {total} records',
        showingRecords: 'Showing {start} - {end} of {total} records',
        selectGroups: 'Please select groups first',
        batchEnable: 'Batch Enable',
        batchDisable: 'Batch Disable',
        batchEnableSuccess: 'Batch enable successful',
        batchDisableSuccess: 'Batch disable successful',
        batchDelete: 'Batch Delete',
        batchDeleteSuccess: 'Batch delete successful',
        confirmDelete: 'Confirm to delete this group?',
        confirmBatchDelete: 'Confirm to delete selected {count} groups?',
        cannotDeleteWithUsers: 'Cannot delete group with users',
        viewMembers: 'View Members',
        edit: 'Edit',
        delete: 'Delete',
        enable: 'Enable',
        disable: 'Disable',
        copy: 'Copy',
        clone: 'Clone Group',
        cloneSuccess: 'Group cloned successfully',
        exportMembers: 'Export Members',
        importMembers: 'Import Members',
        statistics: 'Statistics',
        quickSetPermissions: 'Quick Set Permissions',
        nameRequired: 'Group name is required',
        nameExists: 'Group name already exists',
        createSuccess: 'Group created successfully',
        updateSuccess: 'Group updated successfully',
        deleteSuccess: 'Group deleted successfully',
        enableSuccess: 'Enabled successfully',
        disableSuccess: 'Disabled successfully',
        noPermissions: 'No permissions',
        noData: 'No group data',
        totalGroups: 'Total Groups',
        status: {
          active: 'Active',
          disabled: 'Disabled'
        },
        permissions: {
          browse: 'Browse Permission',
          purchase: 'Purchase Permission',
          comment: 'Comment Permission',
          vipDiscount: 'VIP Discount',
          prioritySupport: 'Priority Support',
          exclusiveProducts: 'Exclusive Products',
          merchantManage: 'Merchant Management',
          productPublish: 'Product Publishing'
        },
        table: {
          title: 'Group List',
          name: 'Group Name',
          description: 'Description',
          userCount: 'User Count',
          permissions: 'Permissions',
          status: 'Status',
          updateTime: 'Update Time',
          actions: 'Actions'
        },
        form: {
          name: 'Group Name',
          namePlaceholder: 'Enter group name',
          description: 'Group Description',
          descriptionPlaceholder: 'Enter group description',
          color: 'Group Color',
          permissions: 'Group Permissions',
          save: 'Save',
          cancel: 'Cancel'
        },
        modal: {
          createTitle: 'Create Group',
          editTitle: 'Edit Group',
          cloneTitle: 'Clone Group'
        },
        drawer: {
          membersTitle: 'Member Management',
          statisticsTitle: 'Usage Statistics',
          memberCount: 'Member Count',
          noMembers: 'No members in this group',
          addMember: 'Add Member',
          removeMember: 'Remove Member',
          searchUser: 'Search and select user',
          addToGroup: 'Add to Group'
        }
      },
      tags: {
        title: 'User Tags',
        description: 'Create and manage user tags for user classification',
        createTag: 'Create Tag',
        editTag: 'Edit Tag',
        searchPlaceholder: 'Search tag name or description',
        selectCategory: 'Select Category',
        selectStatus: 'Select Status',
        selectedCount: 'Selected {count} items',
        totalCount: 'Total {total} records',
        selectTags: 'Please select tags first',
        batchEnable: 'Batch Enable',
        batchDisable: 'Batch Disable',
        batchDelete: 'Batch Delete',
        batchEnableSuccess: 'Batch enable successful',
        batchDisableSuccess: 'Batch disable successful',
        confirmDelete: 'Confirm to delete this tag?',
        viewUsers: 'View Users',
        nameRequired: 'Tag name is required',
        categoryRequired: 'Tag category is required',
        createSuccess: 'Tag created successfully',
        updateSuccess: 'Tag updated successfully',
        totalTags: 'Total Tags',
        users: 'users',
        tagList: 'Tag List',
        categories: {
          userStatus: 'User Status',
          consumeBehavior: 'Consume Behavior',
          regionDistribution: 'Regional Distribution',
          deviceType: 'Device Type',
          userType: 'User Type'
        },
        status: {
          active: 'Active',
          disabled: 'Disabled'
        },
        table: {
          name: 'Tag Name',
          description: 'Description',
          category: 'Category',
          userCount: 'User Count',
          status: 'Status',
          updateTime: 'Update Time',
          actions: 'Actions'
        },
        form: {
          name: 'Tag Name',
          namePlaceholder: 'Enter tag name',
          description: 'Tag Description',
          descriptionPlaceholder: 'Enter tag description',
          category: 'Tag Category',
          categoryPlaceholder: 'Select tag category',
          color: 'Tag Color'
        }
      }
    }
  },
  form: {
    required: 'Cannot be empty',
    userName: {
      required: 'Please enter user name',
      invalid: 'User name format is incorrect'
    },
    phone: {
      required: 'Please enter phone number',
      invalid: 'Phone number format is incorrect'
    },
    pwd: {
      required: 'Please enter password',
      invalid: '6-18 characters, including letters, numbers, and underscores'
    },
    confirmPwd: {
      required: 'Please enter password again',
      invalid: 'The two passwords are inconsistent'
    },
    code: {
      required: 'Please enter verification code',
      invalid: 'Verification code format is incorrect'
    },
    email: {
      required: 'Please enter email',
      invalid: 'Email format is incorrect'
    }
  },
  dropdown: {
    closeCurrent: 'Close Current',
    closeOther: 'Close Other',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeAll: 'Close All'
  },
  icon: {
    themeConfig: 'Theme Configuration',
    themeSchema: 'Theme Schema',
    lang: 'Switch Language',
    fullscreen: 'Fullscreen',
    fullscreenExit: 'Exit Fullscreen',
    reload: 'Reload Page',
    collapse: 'Collapse Menu',
    expand: 'Expand Menu',
    pin: 'Pin',
    unpin: 'Unpin'
  },
  datatable: {
    itemCount: 'Total {total} items'
  }
};

export default local;
