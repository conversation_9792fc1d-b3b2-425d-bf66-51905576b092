<!--
 +----------------------------------------------------------------------
 | FutureShop[ FutureShop电商系统 ]
 +----------------------------------------------------------------------
 | Copyright (c) 2024~2025 https://shop.bwzj.top All rights reserved.
 +----------------------------------------------------------------------
 | Licensed FutureShop并不是自由软件，未经许可不能去掉FutureShop相关版权
 +----------------------------------------------------------------------
 | Author: 暴龙战士 <<EMAIL>>
 +----------------------------------------------------------------------
-->
<script setup lang="ts">
import { watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useEcharts } from '@/hooks/common/echarts';
import { $t } from '@/locales';

defineOptions({
  name: 'SalesChart'
});

const appStore = useAppStore();

const { domRef, updateOptions } = useEcharts(() => ({
  title: {
    text: $t('page.home.salesTrend'),
    left: 'left',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
    formatter: (params: any) => {
      let result = `${params[0].axisValue}<br/>`;
      params.forEach((param: any) => {
        result += `${param.marker}${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`;
      });
      return result;
    }
  },
  legend: {
    data: [$t('page.home.salesAmount'), $t('page.home.orderAmount')],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '15%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [
      '01-01', '01-02', '01-03', '01-04', '01-05', '01-06', '01-07',
      '01-08', '01-09', '01-10', '01-11', '01-12', '01-13', '01-14'
    ]
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      formatter: '¥{value}'
    }
  },
  series: [
    {
      name: $t('page.home.salesAmount'),
      type: 'line',
      stack: 'Total',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        opacity: 0.3
      },
      emphasis: {
        focus: 'series'
      },
      data: [12000, 13200, 10100, 13400, 9000, 23000, 21000, 19000, 14000, 16000, 18000, 22000, 25000, 28000],
      itemStyle: {
        color: '#3b82f6'
      }
    },
    {
      name: $t('page.home.orderAmount'),
      type: 'line',
      stack: 'Total',
      smooth: true,
      lineStyle: {
        width: 3
      },
      areaStyle: {
        opacity: 0.3
      },
      emphasis: {
        focus: 'series'
      },
      data: [8000, 9200, 7100, 9400, 6000, 15000, 14000, 12000, 9000, 11000, 12000, 15000, 17000, 19000],
      itemStyle: {
        color: '#10b981'
      }
    }
  ]
}));

watch(
  () => appStore.locale,
  () => {
    updateOptions();
  }
);
</script>

<template>
  <div class="h-400px">
    <div ref="domRef" class="size-full"></div>
  </div>
</template>

<style scoped></style>
